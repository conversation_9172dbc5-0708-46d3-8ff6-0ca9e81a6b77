"""
Calculus Problems Implementation
Complete implementation of optimization and integration problems
"""

import numpy as np
import scipy.optimize as opt
from scipy import integrate
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import sympy as sp

class CalculusOperations:
    """Class for performing calculus operations"""
    
    @staticmethod
    def find_critical_points(func, grad_func, initial_guess):
        """Find critical points using gradient descent"""
        result = opt.minimize(func, initial_guess, jac=grad_func, method='BFGS')
        return result
    
    @staticmethod
    def hessian_test(hessian_matrix, point):
        """Perform second derivative test using Hessian matrix"""
        eigenvals = np.linalg.eigvals(hessian_matrix)
        
        if np.all(eigenvals > 0):
            return "Local minimum"
        elif np.all(eigenvals < 0):
            return "Local maximum"
        elif np.any(eigenvals > 0) and np.any(eigenvals < 0):
            return "Saddle point"
        else:
            return "Test inconclusive"
    
    @staticmethod
    def numerical_integration_2d(func, x_bounds, y_bounds):
        """Perform 2D numerical integration"""
        result, error = integrate.dblquad(func, x_bounds[0], x_bounds[1], 
                                        lambda x: y_bounds[0], lambda x: y_bounds[1])
        return result, error

def solve_optimization_problem():
    """Solve the optimization problem: minimize f(x,y) = x² + y² - 2x - 4y + 5"""
    print("Calculus Assignment Solutions")
    print("=" * 40)
    print("\nProblem 1: Optimization")
    print("-" * 25)
    
    # Define the function and its derivatives
    def f(vars):
        x, y = vars
        return x**2 + y**2 - 2*x - 4*y + 5
    
    def grad_f(vars):
        x, y = vars
        return np.array([2*x - 2, 2*y - 4])
    
    def hessian_f(vars):
        return np.array([[2, 0], [0, 2]])
    
    print("Function: f(x,y) = x² + y² - 2x - 4y + 5")
    
    # Find critical points analytically
    print("\n1. Finding Critical Points:")
    print("∂f/∂x = 2x - 2 = 0  →  x = 1")
    print("∂f/∂y = 2y - 4 = 0  →  y = 2")
    print("Critical point: (1, 2)")
    
    # Verify numerically
    initial_guess = [0, 0]
    result = CalculusOperations.find_critical_points(f, grad_f, initial_guess)
    print(f"Numerical verification: ({result.x[0]:.6f}, {result.x[1]:.6f})")
    
    # Second derivative test
    print("\n2. Second Derivative Test:")
    critical_point = [1, 2]
    hessian = hessian_f(critical_point)
    print(f"Hessian matrix at (1,2):")
    print(hessian)
    
    det_hessian = np.linalg.det(hessian)
    trace_hessian = np.trace(hessian)
    print(f"Determinant: {det_hessian}")
    print(f"∂²f/∂x²: {hessian[0,0]}")
    
    classification = CalculusOperations.hessian_test(hessian, critical_point)
    print(f"Classification: {classification}")
    
    # Minimum value
    min_value = f(critical_point)
    print(f"\n3. Minimum Value:")
    print(f"f(1, 2) = {min_value}")
    
    return critical_point, min_value

def solve_integration_problem():
    """Solve the integration problem: ∫₀¹ ∫₀¹ xy e^(x+y) dx dy"""
    print("\n\nProblem 2: Integration")
    print("-" * 25)
    print("Evaluate: ∫₀¹ ∫₀¹ xy e^(x+y) dx dy")
    
    # Define the integrand
    def integrand(y, x):  # Note: scipy.integrate.dblquad expects (y, x) order
        return x * y * np.exp(x + y)
    
    # Numerical integration
    result_numerical, error = CalculusOperations.numerical_integration_2d(
        integrand, [0, 1], [0, 1]
    )
    
    print(f"\nNumerical result: {result_numerical:.6f} ± {error:.2e}")
    
    # Analytical solution using sympy
    print("\nAnalytical Solution:")
    x, y = sp.symbols('x y')
    integrand_symbolic = x * y * sp.exp(x + y)
    
    # First integrate with respect to x
    inner_integral = sp.integrate(integrand_symbolic, (x, 0, 1))
    print(f"∫₀¹ xy e^(x+y) dx = {inner_integral}")
    
    # Then integrate with respect to y
    outer_integral = sp.integrate(inner_integral, (y, 0, 1))
    print(f"∫₀¹ [∫₀¹ xy e^(x+y) dx] dy = {outer_integral}")
    
    analytical_result = float(outer_integral)
    print(f"Analytical result: {analytical_result:.6f}")
    
    # Verify the step-by-step calculation
    print("\nStep-by-step verification:")
    print("∫₀¹ ∫₀¹ xy e^(x+y) dx dy = ∫₀¹ ∫₀¹ xy e^x e^y dx dy")
    print("                           = ∫₀¹ y e^y dy ∫₀¹ x e^x dx")
    
    # Calculate ∫₀¹ x e^x dx
    x_integral = sp.integrate(x * sp.exp(x), (x, 0, 1))
    print(f"∫₀¹ x e^x dx = {x_integral} = {float(x_integral):.6f}")
    
    # Calculate ∫₀¹ y e^y dy
    y_integral = sp.integrate(y * sp.exp(y), (y, 0, 1))
    print(f"∫₀¹ y e^y dy = {y_integral} = {float(y_integral):.6f}")
    
    product_result = float(x_integral) * float(y_integral)
    print(f"Product: {float(x_integral):.6f} × {float(y_integral):.6f} = {product_result:.6f}")
    
    return analytical_result

def visualize_optimization():
    """Visualize the optimization problem"""
    try:
        # Create a grid of points
        x = np.linspace(-1, 3, 100)
        y = np.linspace(-1, 4, 100)
        X, Y = np.meshgrid(x, y)
        Z = X**2 + Y**2 - 2*X - 4*Y + 5
        
        # Create 3D plot
        fig = plt.figure(figsize=(15, 5))
        
        # 3D surface plot
        ax1 = fig.add_subplot(131, projection='3d')
        surf = ax1.plot_surface(X, Y, Z, cmap='viridis', alpha=0.7)
        ax1.scatter([1], [2], [0], color='red', s=100, label='Minimum')
        ax1.set_xlabel('x')
        ax1.set_ylabel('y')
        ax1.set_zlabel('f(x,y)')
        ax1.set_title('3D Surface Plot')
        
        # Contour plot
        ax2 = fig.add_subplot(132)
        contour = ax2.contour(X, Y, Z, levels=20)
        ax2.clabel(contour, inline=True, fontsize=8)
        ax2.plot(1, 2, 'ro', markersize=10, label='Minimum (1,2)')
        ax2.set_xlabel('x')
        ax2.set_ylabel('y')
        ax2.set_title('Contour Plot')
        ax2.legend()
        ax2.grid(True)
        
        # Gradient field
        ax3 = fig.add_subplot(133)
        x_grad = np.linspace(-1, 3, 20)
        y_grad = np.linspace(-1, 4, 20)
        X_grad, Y_grad = np.meshgrid(x_grad, y_grad)
        U = 2*X_grad - 2  # ∂f/∂x
        V = 2*Y_grad - 4  # ∂f/∂y
        
        ax3.quiver(X_grad, Y_grad, -U, -V, alpha=0.6)  # Negative gradient points toward minimum
        ax3.plot(1, 2, 'ro', markersize=10, label='Minimum (1,2)')
        ax3.set_xlabel('x')
        ax3.set_ylabel('y')
        ax3.set_title('Negative Gradient Field')
        ax3.legend()
        ax3.grid(True)
        
        plt.tight_layout()
        plt.show()
        
    except ImportError:
        print("Matplotlib not available for visualization")

def demonstrate_additional_calculus():
    """Demonstrate additional calculus concepts"""
    print("\n\nAdditional Calculus Demonstrations")
    print("=" * 40)
    
    # Taylor series expansion
    print("\nTaylor Series Expansion:")
    x = sp.Symbol('x')
    f_sym = sp.exp(x)
    taylor_series = f_sym.series(x, 0, 6)
    print(f"e^x ≈ {taylor_series}")
    
    # Partial derivatives
    print("\nPartial Derivatives:")
    x, y = sp.symbols('x y')
    f_multi = x**3 * y**2 + sp.sin(x*y)
    
    df_dx = sp.diff(f_multi, x)
    df_dy = sp.diff(f_multi, y)
    d2f_dxdy = sp.diff(df_dx, y)
    
    print(f"f(x,y) = {f_multi}")
    print(f"∂f/∂x = {df_dx}")
    print(f"∂f/∂y = {df_dy}")
    print(f"∂²f/∂x∂y = {d2f_dxdy}")
    
    # Limits
    print("\nLimits:")
    limit_result = sp.limit(sp.sin(x)/x, x, 0)
    print(f"lim(x→0) sin(x)/x = {limit_result}")

def main():
    """Main function to run all calculus problems"""
    critical_point, min_value = solve_optimization_problem()
    integration_result = solve_integration_problem()
    
    print(f"\n\nSummary of Results:")
    print("=" * 20)
    print(f"Optimization minimum at: {critical_point} with value: {min_value}")
    print(f"Integration result: {integration_result:.6f}")
    
    visualize_optimization()
    demonstrate_additional_calculus()

if __name__ == "__main__":
    main()
