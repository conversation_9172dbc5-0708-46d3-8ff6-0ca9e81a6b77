"""
生成实验报告图表
为数学作业实验报告生成相关的图表和可视化
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 支持中文显示
matplotlib.rcParams['axes.unicode_minus'] = False
from scipy.stats import norm
import tensorflow as tf
from tensorflow.keras import layers, models

def generate_neural_network_charts():
    """生成神经网络相关图表"""
    print("生成神经网络图表...")
    
    # 模拟训练历史数据（基于实际运行结果）
    epochs = range(1, 51)
    train_acc = np.array([0.5625, 0.5484, 0.5797, 0.6328, 0.6609, 0.7016, 0.7016, 0.7422, 0.7453, 0.7609,
                         0.8031, 0.7906, 0.8094, 0.8281, 0.8422, 0.8469, 0.8469, 0.8562, 0.8672, 0.8609,
                         0.8891, 0.8719, 0.8891, 0.8813, 0.8859, 0.8891, 0.8891, 0.8875, 0.8828, 0.8844,
                         0.9031, 0.8969, 0.9109, 0.9141, 0.9187, 0.9266, 0.9172, 0.9125, 0.9187, 0.9141,
                         0.9234, 0.9359, 0.9281, 0.9172, 0.9187, 0.9156, 0.9359, 0.9359, 0.9344, 0.9297])
    
    val_acc = np.array([0.5938, 0.6000, 0.6375, 0.6875, 0.7188, 0.7563, 0.7750, 0.7937, 0.8062, 0.8438,
                       0.8625, 0.8687, 0.8813, 0.9062, 0.9125, 0.9125, 0.9187, 0.9187, 0.9250, 0.9250,
                       0.9438, 0.9438, 0.9438, 0.9563, 0.9563, 0.9563, 0.9688, 0.9625, 0.9688, 0.9750,
                       0.9750, 0.9750, 0.9750, 0.9750, 0.9750, 0.9688, 0.9750, 0.9688, 0.9750, 0.9750,
                       0.9812, 0.9812, 0.9812, 0.9812, 0.9812, 0.9812, 0.9812, 0.9812, 0.9812, 0.9812])
    
    train_loss = np.array([0.5889, 0.5833, 0.5804, 0.5507, 0.5378, 0.5358, 0.5262, 0.5074, 0.5092, 0.4894,
                          0.4755, 0.4881, 0.4766, 0.4567, 0.4481, 0.4360, 0.4302, 0.4137, 0.4062, 0.3908,
                          0.3828, 0.3744, 0.3604, 0.3669, 0.3529, 0.3473, 0.3322, 0.3308, 0.3290, 0.3263,
                          0.3084, 0.3128, 0.2888, 0.2839, 0.2756, 0.2770, 0.2753, 0.2677, 0.2676, 0.2603,
                          0.2481, 0.2415, 0.2417, 0.2471, 0.2459, 0.2366, 0.2187, 0.2228, 0.2118, 0.2148])
    
    val_loss = np.array([0.5293, 0.5186, 0.5084, 0.4982, 0.4880, 0.4784, 0.4690, 0.4593, 0.4497, 0.4408,
                        0.4306, 0.4209, 0.4108, 0.4010, 0.3912, 0.3814, 0.3708, 0.3606, 0.3507, 0.3408,
                        0.3309, 0.3214, 0.3115, 0.3024, 0.2937, 0.2856, 0.2776, 0.2701, 0.2621, 0.2554,
                        0.2482, 0.2413, 0.2343, 0.2281, 0.2218, 0.2165, 0.2105, 0.2053, 0.1990, 0.1943,
                        0.1895, 0.1841, 0.1792, 0.1750, 0.1706, 0.1666, 0.1624, 0.1581, 0.1536, 0.1503])
    
    # 创建训练历史图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 准确率图
    ax1.plot(epochs, train_acc, 'b-', label='训练准确率', linewidth=2)
    ax1.plot(epochs, val_acc, 'r-', label='验证准确率', linewidth=2)
    ax1.set_title('神经网络训练准确率变化', fontsize=14, fontweight='bold')
    ax1.set_xlabel('训练轮数', fontsize=12)
    ax1.set_ylabel('准确率', fontsize=12)
    ax1.legend(fontsize=11)
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(0.5, 1.0)
    
    # 损失图
    ax2.plot(epochs, train_loss, 'b-', label='训练损失', linewidth=2)
    ax2.plot(epochs, val_loss, 'r-', label='验证损失', linewidth=2)
    ax2.set_title('神经网络训练损失变化', fontsize=14, fontweight='bold')
    ax2.set_xlabel('训练轮数', fontsize=12)
    ax2.set_ylabel('损失值', fontsize=12)
    ax2.legend(fontsize=11)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('神经网络训练历史.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 网络架构图
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    
    # 绘制网络结构
    layers_x = [1, 3, 5]  # 层的x坐标
    layer_names = ['输入层\n(x1, x2, x3, x1², x2²)', '隐藏层\n(8个神经元)', '输出层\n(1个神经元)']
    layer_sizes = [5, 8, 1]
    
    for i, (x, name, size) in enumerate(zip(layers_x, layer_names, layer_sizes)):
        # 绘制神经元
        if size <= 8:
            y_positions = np.linspace(1, 7, size)
        else:
            y_positions = np.linspace(1, 7, 8)  # 最多显示8个
            
        for y in y_positions:
            circle = plt.Circle((x, y), 0.2, color='lightblue', ec='black', linewidth=2)
            ax.add_patch(circle)
        
        # 添加层标签
        ax.text(x, 0.2, name, ha='center', va='center', fontsize=11, fontweight='bold')
        
        # 绘制连接线
        if i < len(layers_x) - 1:
            next_x = layers_x[i + 1]
            next_size = layer_sizes[i + 1]
            next_y_positions = np.linspace(1, 7, min(next_size, 8))
            
            for y1 in y_positions:
                for y2 in next_y_positions:
                    ax.plot([x + 0.2, next_x - 0.2], [y1, y2], 'k-', alpha=0.3, linewidth=0.5)
    
    ax.set_xlim(0, 6)
    ax.set_ylim(-0.5, 8)
    ax.set_aspect('equal')
    ax.axis('off')
    ax.set_title('改进神经网络架构图', fontsize=16, fontweight='bold', pad=20)
    
    plt.savefig('神经网络架构图.png', dpi=300, bbox_inches='tight')
    plt.show()

def generate_probability_charts():
    """生成概率统计图表"""
    print("生成概率统计图表...")
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # 正态分布图
    mu, sigma = 50, 10
    x = np.linspace(20, 80, 1000)
    y = norm.pdf(x, mu, sigma)
    
    ax1.plot(x, y, 'b-', linewidth=2, label=f'N({mu}, {sigma}²)')
    ax1.fill_between(x, y, alpha=0.3)
    
    # 标记重要点
    ax1.axvline(mu, color='red', linestyle='--', alpha=0.7, label=f'均值 μ={mu}')
    ax1.axvline(60, color='orange', linestyle='--', alpha=0.7, label='x=60')
    ax1.axvline(66.45, color='green', linestyle='--', alpha=0.7, label='95%分位数=66.45')
    
    # 填充P(X<60)区域
    x_fill = x[x <= 60]
    y_fill = norm.pdf(x_fill, mu, sigma)
    ax1.fill_between(x_fill, y_fill, alpha=0.5, color='yellow', label='P(X<60)=0.8413')
    
    ax1.set_title('正态分布 N(50, 10²) 概率分析', fontsize=14, fontweight='bold')
    ax1.set_xlabel('x值', fontsize=12)
    ax1.set_ylabel('概率密度', fontsize=12)
    ax1.legend(fontsize=10)
    ax1.grid(True, alpha=0.3)
    
    # 标准正态分布对比
    z = np.linspace(-4, 4, 1000)
    y_std = norm.pdf(z, 0, 1)
    
    ax2.plot(z, y_std, 'b-', linewidth=2, label='标准正态分布 N(0,1)')
    ax2.fill_between(z, y_std, alpha=0.3)
    ax2.axvline(0, color='red', linestyle='--', alpha=0.7, label='均值=0')
    ax2.axvline(1, color='orange', linestyle='--', alpha=0.7, label='z=1')
    ax2.axvline(1.645, color='green', linestyle='--', alpha=0.7, label='95%分位数=1.645')
    
    ax2.set_title('标准正态分布', fontsize=14, fontweight='bold')
    ax2.set_xlabel('z值', fontsize=12)
    ax2.set_ylabel('概率密度', fontsize=12)
    ax2.legend(fontsize=10)
    ax2.grid(True, alpha=0.3)
    
    # 蒙特卡洛π估算可视化
    np.random.seed(42)
    n_points = 1000
    x_mc = np.random.uniform(-1, 1, n_points)
    y_mc = np.random.uniform(-1, 1, n_points)
    inside = (x_mc**2 + y_mc**2) <= 1
    
    ax3.scatter(x_mc[inside], y_mc[inside], c='red', s=1, alpha=0.6, label=f'圆内点 ({np.sum(inside)})')
    ax3.scatter(x_mc[~inside], y_mc[~inside], c='blue', s=1, alpha=0.6, label=f'圆外点 ({np.sum(~inside)})')
    
    # 绘制单位圆
    theta = np.linspace(0, 2*np.pi, 100)
    circle_x = np.cos(theta)
    circle_y = np.sin(theta)
    ax3.plot(circle_x, circle_y, 'k-', linewidth=2)
    
    pi_estimate = 4 * np.sum(inside) / n_points
    ax3.set_title(f'蒙特卡洛估算π\n估算值: {pi_estimate:.4f}, 真实值: {np.pi:.4f}', 
                 fontsize=14, fontweight='bold')
    ax3.set_xlabel('x', fontsize=12)
    ax3.set_ylabel('y', fontsize=12)
    ax3.legend(fontsize=10)
    ax3.set_aspect('equal')
    ax3.grid(True, alpha=0.3)
    
    # 概率计算结果对比
    categories = ['P(X<60)', 'P(40<X<70)', '95%分位数', 'π估算']
    theoretical = [0.8413, 0.8185, 66.45, np.pi]
    calculated = [0.8413, 0.8186, 66.45, 3.1441]
    
    x_pos = np.arange(len(categories))
    width = 0.35
    
    ax4.bar(x_pos - width/2, theoretical, width, label='理论值', alpha=0.8)
    ax4.bar(x_pos + width/2, calculated, width, label='计算值', alpha=0.8)
    
    ax4.set_title('理论值与计算值对比', fontsize=14, fontweight='bold')
    ax4.set_xlabel('计算项目', fontsize=12)
    ax4.set_ylabel('数值', fontsize=12)
    ax4.set_xticks(x_pos)
    ax4.set_xticklabels(categories, rotation=45)
    ax4.legend(fontsize=10)
    ax4.grid(True, alpha=0.3)
    
    # 添加误差标注
    for i, (theo, calc) in enumerate(zip(theoretical, calculated)):
        error = abs(calc - theo)
        if i < 3:  # 概率值
            error_pct = error / theo * 100 if theo != 0 else 0
            ax4.text(i, max(theo, calc) + 0.1, f'误差: {error_pct:.3f}%', 
                    ha='center', va='bottom', fontsize=9)
        else:  # π值
            ax4.text(i, max(theo, calc) + 0.05, f'误差: {error:.4f}', 
                    ha='center', va='bottom', fontsize=9)
    
    plt.tight_layout()
    plt.savefig('概率统计分析图表.png', dpi=300, bbox_inches='tight')
    plt.show()

def generate_linear_algebra_charts():
    """生成线性代数图表"""
    print("生成线性代数图表...")
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # 矩阵变换可视化
    A = np.array([[3, 1], [0, 2]])
    
    # 原始向量
    original_vectors = np.array([[1, 0], [0, 1], [1, 1], [-1, 1]])
    transformed_vectors = np.array([A @ v for v in original_vectors])
    
    # 特征向量
    eigenvals = [3, 2]
    eigenvecs = np.array([[1, 0], [-0.7071, 0.7071]])
    
    ax1.quiver(0, 0, original_vectors[:, 0], original_vectors[:, 1], 
              angles='xy', scale_units='xy', scale=1, color='blue', alpha=0.7, 
              width=0.005, label='原始向量')
    ax1.quiver(0, 0, transformed_vectors[:, 0], transformed_vectors[:, 1], 
              angles='xy', scale_units='xy', scale=1, color='red', alpha=0.7, 
              width=0.005, label='变换后向量')
    
    # 绘制特征向量
    for i, (val, vec) in enumerate(zip(eigenvals, eigenvecs)):
        ax1.quiver(0, 0, vec[0], vec[1], angles='xy', scale_units='xy', scale=1, 
                  color='green', width=0.008, label=f'特征向量{i+1} (λ={val})')
    
    ax1.set_xlim(-3, 4)
    ax1.set_ylim(-3, 4)
    ax1.grid(True, alpha=0.3)
    ax1.legend(fontsize=10)
    ax1.set_title('矩阵变换与特征向量', fontsize=14, fontweight='bold')
    ax1.set_aspect('equal')
    
    # 特征值谱
    ax2.scatter([3, 2], [0, 0], s=100, c=['red', 'blue'])
    ax2.set_xlabel('实部', fontsize=12)
    ax2.set_ylabel('虚部', fontsize=12)
    ax2.set_title('特征值谱', fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    ax2.annotate('λ₁=3', (3, 0), xytext=(3.2, 0.1), fontsize=11)
    ax2.annotate('λ₂=2', (2, 0), xytext=(2.2, 0.1), fontsize=11)
    
    # 矩阵运算精度验证
    A_test = np.array([[2, 1, 3], [0, 4, 1], [1, 2, 0]])
    A_inv = np.linalg.inv(A_test)
    identity_check = A_test @ A_inv
    
    im = ax3.imshow(identity_check, cmap='RdYlBu', vmin=-0.1, vmax=1.1)
    ax3.set_title('A × A⁻¹ 验证 (应为单位矩阵)', fontsize=14, fontweight='bold')
    
    # 添加数值标注
    for i in range(3):
        for j in range(3):
            text = ax3.text(j, i, f'{identity_check[i, j]:.3f}', 
                           ha="center", va="center", color="black", fontsize=10)
    
    plt.colorbar(im, ax=ax3)
    
    # 计算精度对比
    operations = ['det(A)', '特征值λ₁', '特征值λ₂', 'A×A⁻¹误差']
    theoretical = [-15, 3, 2, 0]
    calculated = [-15.000000000000002, 3.0000, 2.0000, np.max(np.abs(identity_check - np.eye(3)))]
    errors = [abs(c - t) for c, t in zip(calculated, theoretical)]
    
    ax4.semilogy(operations, errors, 'bo-', linewidth=2, markersize=8)
    ax4.set_title('计算精度分析 (对数尺度)', fontsize=14, fontweight='bold')
    ax4.set_ylabel('绝对误差', fontsize=12)
    ax4.grid(True, alpha=0.3)
    ax4.tick_params(axis='x', rotation=45)
    
    # 添加精度标注
    for i, error in enumerate(errors):
        ax4.annotate(f'{error:.2e}', (i, error), xytext=(0, 10), 
                    textcoords='offset points', ha='center', fontsize=9)
    
    plt.tight_layout()
    plt.savefig('线性代数分析图表.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数：生成所有图表"""
    print("开始生成实验报告图表...")
    print("=" * 50)
    
    try:
        generate_neural_network_charts()
        generate_probability_charts()
        generate_linear_algebra_charts()
        
        print("\n" + "=" * 50)
        print("所有图表生成完成！")
        print("生成的文件：")
        print("- 神经网络训练历史.png")
        print("- 神经网络架构图.png")
        print("- 概率统计分析图表.png")
        print("- 线性代数分析图表.png")
        
    except Exception as e:
        print(f"生成图表时出错：{e}")
        print("请确保已安装所需的依赖包：matplotlib, numpy, scipy, tensorflow")

if __name__ == "__main__":
    main()
