# Mathematical Assignments Solutions

## Assignment 1: Neural Network with Modified Input Features

### Problem Statement
Design and implement a neural network where the input features are (x1, x2, x3, x1², x2²) instead of the traditional (x1, x2, x3).

### Solution

#### 1. Network Architecture
- **Input Layer**: 5 neurons (x1, x2, x3, x1², x2²)
- **Hidden Layer**: 8 neurons with ReLU activation
- **Output Layer**: Depends on the specific task (classification/regression)

#### 2. Mathematical Formulation

**Input Feature Vector:**
```
X = [x1, x2, x3, x1², x2²]ᵀ
```

**Forward Propagation:**
```
Hidden Layer: h = σ(W₁X + b₁)
Output Layer: y = σ(W₂h + b₂)
```

Where:
- W₁ ∈ ℝ⁸ˣ⁵ (weights from input to hidden layer)
- W₂ ∈ ℝᵏˣ⁸ (weights from hidden to output layer, k = number of output classes)
- b₁, b₂ are bias vectors
- σ is the activation function

#### 3. Implementation Code (Python)

```python
import numpy as np
import tensorflow as tf
from tensorflow.keras import layers, models

def create_modified_neural_network(input_dim=3, hidden_units=8, output_units=1):
    """
    Create a neural network with modified input features
    """
    model = models.Sequential([
        # Input preprocessing layer to add squared terms
        layers.Lambda(lambda x: tf.concat([
            x,  # original features [x1, x2, x3]
            tf.square(x[:, :2])  # squared terms [x1², x2²]
        ], axis=1)),
        
        # Hidden layer
        layers.Dense(hidden_units, activation='relu', name='hidden_layer'),
        
        # Output layer
        layers.Dense(output_units, activation='sigmoid', name='output_layer')
    ])
    
    return model

# Example usage
model = create_modified_neural_network()
model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])

# Generate sample data
X_sample = np.random.randn(100, 3)  # 100 samples, 3 features
y_sample = np.random.randint(0, 2, (100, 1))  # Binary classification

# Train the model
model.fit(X_sample, y_sample, epochs=50, batch_size=16, verbose=1)
```

#### 4. Advantages of Modified Input Features

1. **Non-linear Feature Engineering**: Adding x1² and x2² introduces non-linearity
2. **Enhanced Pattern Recognition**: Squared terms can capture quadratic relationships
3. **Improved Decision Boundaries**: More complex decision boundaries possible

#### 5. Mathematical Analysis

**Gradient Computation:**
For backpropagation, we need to compute gradients with respect to the modified input:

```
∂L/∂x1 = ∂L/∂h · ∂h/∂x1 + ∂L/∂h · ∂h/∂(x1²) · 2x1
∂L/∂x2 = ∂L/∂h · ∂h/∂x2 + ∂L/∂h · ∂h/∂(x2²) · 2x2
∂L/∂x3 = ∂L/∂h · ∂h/∂x3
```

## Assignment 2: Linear Algebra Problems

### Problem 1: Matrix Operations

Given matrices A and B, perform the following operations:

```
A = [2  1  3]    B = [1  0]
    [0  4  1]        [2  1]
    [1  2  0]        [0  3]
```

**Solutions:**

1. **Matrix Multiplication AB:**
```
AB = [2×1+1×2+3×0  2×0+1×1+3×3] = [4   10]
     [0×1+4×2+1×0  0×0+4×1+1×3]   [8    7]
     [1×1+2×2+0×0  1×0+2×1+0×3]   [5    2]
```

2. **Determinant of A:**
```
det(A) = 2(4×0 - 1×2) - 1(0×0 - 1×1) + 3(0×2 - 4×1)
       = 2(-2) - 1(-1) + 3(-4)
       = -4 + 1 - 12 = -15
```

3. **Inverse of A (if exists):**
Since det(A) = -15 ≠ 0, A⁻¹ exists.

Using the adjugate method:
```
A⁻¹ = (1/det(A)) × adj(A)
```

### Problem 2: Eigenvalues and Eigenvectors

For matrix C = [3  1]
              [0  2]

**Solution:**

1. **Characteristic Polynomial:**
```
det(C - λI) = det([3-λ  1  ]) = (3-λ)(2-λ) = λ² - 5λ + 6
                 [0   2-λ]
```

2. **Eigenvalues:**
```
λ² - 5λ + 6 = 0
(λ - 2)(λ - 3) = 0
λ₁ = 2, λ₂ = 3
```

3. **Eigenvectors:**
For λ₁ = 2: (C - 2I)v₁ = 0
```
[1  1][v₁₁] = [0]  →  v₁ = [1]
[0  0][v₁₂]   [0]           [-1]
```

For λ₂ = 3: (C - 3I)v₂ = 0
```
[0  1][v₂₁] = [0]  →  v₂ = [1]
[0 -1][v₂₂]   [0]           [0]
```

## Assignment 3: Calculus Problems

### Problem 1: Optimization

Find the minimum of f(x, y) = x² + y² - 2x - 4y + 5

**Solution:**

1. **Find Critical Points:**
```
∂f/∂x = 2x - 2 = 0  →  x = 1
∂f/∂y = 2y - 4 = 0  →  y = 2
```

2. **Second Derivative Test:**
```
∂²f/∂x² = 2,  ∂²f/∂y² = 2,  ∂²f/∂x∂y = 0

Hessian = [2  0]  →  det(H) = 4 > 0, ∂²f/∂x² = 2 > 0
          [0  2]
```

Therefore, (1, 2) is a local minimum.

3. **Minimum Value:**
```
f(1, 2) = 1 + 4 - 2 - 8 + 5 = 0
```

### Problem 2: Integration

Evaluate ∫₀¹ ∫₀¹ xy e^(x+y) dx dy

**Solution:**

Using integration by parts and properties of exponential functions:

```
∫₀¹ ∫₀¹ xy e^(x+y) dx dy = ∫₀¹ ∫₀¹ xy e^x e^y dx dy
                           = ∫₀¹ y e^y dy ∫₀¹ x e^x dx
```

For ∫₀¹ x e^x dx (integration by parts):
```
u = x, dv = e^x dx
du = dx, v = e^x
∫ x e^x dx = x e^x - ∫ e^x dx = x e^x - e^x = e^x(x - 1)
```

Evaluating from 0 to 1: e¹(1-1) - e⁰(0-1) = 0 - (-1) = 1

Similarly, ∫₀¹ y e^y dy = 1

Therefore: ∫₀¹ ∫₀¹ xy e^(x+y) dx dy = 1 × 1 = 1

## Assignment 4: Probability and Statistics

### Problem: Normal Distribution Analysis

Given a normal distribution with μ = 50 and σ = 10, find:

1. P(X < 60)
2. P(40 < X < 70)
3. The 95th percentile

**Solutions:**

1. **P(X < 60):**
```
Z = (60 - 50)/10 = 1
P(X < 60) = P(Z < 1) = 0.8413
```

2. **P(40 < X < 70):**
```
Z₁ = (40 - 50)/10 = -1
Z₂ = (70 - 50)/10 = 2
P(40 < X < 70) = P(-1 < Z < 2) = Φ(2) - Φ(-1) = 0.9772 - 0.1587 = 0.8185
```

3. **95th Percentile:**
```
P(X < x) = 0.95
P(Z < (x-50)/10) = 0.95
(x-50)/10 = 1.645
x = 50 + 16.45 = 66.45
```

## Conclusion

These solutions demonstrate various mathematical concepts including:
- Modified neural network architectures with feature engineering
- Linear algebra operations and eigenvalue analysis
- Calculus optimization and integration techniques
- Probability distributions and statistical analysis

Each solution includes step-by-step mathematical derivations and, where applicable, implementation code.
