"""
Probability and Statistics Implementation
Complete implementation of normal distribution analysis and statistical operations
"""

import numpy as np
import scipy.stats as stats
import matplotlib.pyplot as plt
from scipy.stats import norm, chi2, t
import pandas as pd

class ProbabilityStatistics:
    """Class for probability and statistics operations"""
    
    def __init__(self):
        self.distributions = {
            'normal': norm,
            'chi2': chi2,
            't': t
        }
    
    @staticmethod
    def normal_probability(x, mu=0, sigma=1):
        """Calculate probability for normal distribution"""
        return norm.cdf(x, loc=mu, scale=sigma)
    
    @staticmethod
    def normal_percentile(p, mu=0, sigma=1):
        """Calculate percentile for normal distribution"""
        return norm.ppf(p, loc=mu, scale=sigma)
    
    @staticmethod
    def z_score(x, mu, sigma):
        """Calculate z-score"""
        return (x - mu) / sigma
    
    @staticmethod
    def confidence_interval(data, confidence=0.95):
        """Calculate confidence interval for mean"""
        n = len(data)
        mean = np.mean(data)
        std_err = stats.sem(data)
        h = std_err * stats.t.ppf((1 + confidence) / 2, n - 1)
        return mean - h, mean + h
    
    @staticmethod
    def hypothesis_test_one_sample(data, mu0, alpha=0.05):
        """Perform one-sample t-test"""
        t_stat, p_value = stats.ttest_1samp(data, mu0)
        return t_stat, p_value, p_value < alpha

def solve_normal_distribution_problems():
    """Solve the normal distribution problems from the assignment"""
    print("Probability and Statistics Assignment Solutions")
    print("=" * 50)
    print("\nNormal Distribution Analysis")
    print("Given: μ = 50, σ = 10")
    print("-" * 30)
    
    mu = 50
    sigma = 10
    
    # Problem 1: P(X < 60)
    print("1. P(X < 60):")
    x1 = 60
    z1 = ProbabilityStatistics.z_score(x1, mu, sigma)
    prob1 = ProbabilityStatistics.normal_probability(x1, mu, sigma)
    
    print(f"   Z = (60 - 50)/10 = {z1}")
    print(f"   P(X < 60) = P(Z < {z1}) = {prob1:.4f}")
    print(f"   Expected: 0.8413")
    
    # Problem 2: P(40 < X < 70)
    print("\n2. P(40 < X < 70):")
    x2_lower = 40
    x2_upper = 70
    z2_lower = ProbabilityStatistics.z_score(x2_lower, mu, sigma)
    z2_upper = ProbabilityStatistics.z_score(x2_upper, mu, sigma)
    
    prob2_lower = ProbabilityStatistics.normal_probability(x2_lower, mu, sigma)
    prob2_upper = ProbabilityStatistics.normal_probability(x2_upper, mu, sigma)
    prob2 = prob2_upper - prob2_lower
    
    print(f"   Z₁ = (40 - 50)/10 = {z2_lower}")
    print(f"   Z₂ = (70 - 50)/10 = {z2_upper}")
    print(f"   P(40 < X < 70) = P({z2_lower} < Z < {z2_upper})")
    print(f"                   = Φ({z2_upper}) - Φ({z2_lower})")
    print(f"                   = {prob2_upper:.4f} - {prob2_lower:.4f}")
    print(f"                   = {prob2:.4f}")
    print(f"   Expected: 0.8185")
    
    # Problem 3: 95th percentile
    print("\n3. 95th Percentile:")
    percentile_95 = ProbabilityStatistics.normal_percentile(0.95, mu, sigma)
    z_95 = ProbabilityStatistics.normal_percentile(0.95, 0, 1)
    
    print(f"   P(X < x) = 0.95")
    print(f"   P(Z < (x-50)/10) = 0.95")
    print(f"   (x-50)/10 = {z_95:.3f}")
    print(f"   x = 50 + 10 × {z_95:.3f} = {percentile_95:.2f}")
    print(f"   Expected: 66.45")
    
    return prob1, prob2, percentile_95

def demonstrate_statistical_tests():
    """Demonstrate various statistical tests"""
    print("\n\nStatistical Tests Demonstration")
    print("=" * 35)
    
    # Generate sample data
    np.random.seed(42)
    sample_data = np.random.normal(50, 10, 100)
    
    print(f"Sample data (n=100):")
    print(f"Sample mean: {np.mean(sample_data):.2f}")
    print(f"Sample std: {np.std(sample_data, ddof=1):.2f}")
    
    # Confidence interval
    ci_lower, ci_upper = ProbabilityStatistics.confidence_interval(sample_data, 0.95)
    print(f"\n95% Confidence Interval for mean: ({ci_lower:.2f}, {ci_upper:.2f})")
    
    # Hypothesis test
    print(f"\nHypothesis Test:")
    print(f"H₀: μ = 50 vs H₁: μ ≠ 50")
    t_stat, p_value, reject_null = ProbabilityStatistics.hypothesis_test_one_sample(sample_data, 50)
    print(f"t-statistic: {t_stat:.4f}")
    print(f"p-value: {p_value:.4f}")
    print(f"Reject H₀ at α=0.05: {reject_null}")
    
    # Normality test
    shapiro_stat, shapiro_p = stats.shapiro(sample_data)
    print(f"\nShapiro-Wilk Normality Test:")
    print(f"Statistic: {shapiro_stat:.4f}")
    print(f"p-value: {shapiro_p:.4f}")
    print(f"Data appears normal: {shapiro_p > 0.05}")

def demonstrate_other_distributions():
    """Demonstrate other probability distributions"""
    print("\n\nOther Probability Distributions")
    print("=" * 35)
    
    # Chi-square distribution
    print("Chi-square Distribution (df=5):")
    chi2_dist = chi2(df=5)
    chi2_mean = chi2_dist.mean()
    chi2_var = chi2_dist.var()
    print(f"Mean: {chi2_mean}")
    print(f"Variance: {chi2_var}")
    print(f"P(X < 10): {chi2_dist.cdf(10):.4f}")
    
    # t-distribution
    print(f"\nt-Distribution (df=10):")
    t_dist = t(df=10)
    t_95_percentile = t_dist.ppf(0.95)
    print(f"95th percentile: {t_95_percentile:.4f}")
    print(f"P(-2 < T < 2): {t_dist.cdf(2) - t_dist.cdf(-2):.4f}")
    
    # Binomial distribution
    print(f"\nBinomial Distribution (n=20, p=0.3):")
    from scipy.stats import binom
    binom_dist = binom(n=20, p=0.3)
    binom_mean = binom_dist.mean()
    binom_var = binom_dist.var()
    print(f"Mean: {binom_mean}")
    print(f"Variance: {binom_var}")
    print(f"P(X = 5): {binom_dist.pmf(5):.4f}")
    print(f"P(X ≤ 8): {binom_dist.cdf(8):.4f}")

def visualize_distributions():
    """Visualize probability distributions"""
    try:
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # Normal distribution
        x_norm = np.linspace(-4, 4, 1000)
        y_norm = norm.pdf(x_norm, 0, 1)
        ax1.plot(x_norm, y_norm, 'b-', linewidth=2, label='Standard Normal')
        ax1.fill_between(x_norm, y_norm, alpha=0.3)
        ax1.axvline(0, color='red', linestyle='--', alpha=0.7, label='Mean')
        ax1.set_title('Standard Normal Distribution')
        ax1.set_xlabel('z')
        ax1.set_ylabel('Probability Density')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Normal distribution with μ=50, σ=10
        x_norm2 = np.linspace(20, 80, 1000)
        y_norm2 = norm.pdf(x_norm2, 50, 10)
        ax2.plot(x_norm2, y_norm2, 'g-', linewidth=2, label='N(50, 10²)')
        ax2.fill_between(x_norm2, y_norm2, alpha=0.3)
        ax2.axvline(50, color='red', linestyle='--', alpha=0.7, label='Mean=50')
        ax2.axvline(60, color='orange', linestyle='--', alpha=0.7, label='x=60')
        ax2.set_title('Normal Distribution (μ=50, σ=10)')
        ax2.set_xlabel('x')
        ax2.set_ylabel('Probability Density')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # Chi-square distribution
        x_chi2 = np.linspace(0, 20, 1000)
        for df in [1, 3, 5, 10]:
            y_chi2 = chi2.pdf(x_chi2, df)
            ax3.plot(x_chi2, y_chi2, linewidth=2, label=f'df={df}')
        ax3.set_title('Chi-square Distribution')
        ax3.set_xlabel('x')
        ax3.set_ylabel('Probability Density')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # t-distribution
        x_t = np.linspace(-4, 4, 1000)
        y_norm_std = norm.pdf(x_t, 0, 1)
        ax4.plot(x_t, y_norm_std, 'k-', linewidth=2, label='Standard Normal')
        for df in [1, 3, 10]:
            y_t = t.pdf(x_t, df)
            ax4.plot(x_t, y_t, linewidth=2, label=f't(df={df})')
        ax4.set_title('t-Distribution vs Normal')
        ax4.set_xlabel('x')
        ax4.set_ylabel('Probability Density')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
        
    except ImportError:
        print("Matplotlib not available for visualization")

def monte_carlo_simulation():
    """Demonstrate Monte Carlo simulation"""
    print("\n\nMonte Carlo Simulation")
    print("=" * 25)
    
    # Estimate π using Monte Carlo
    n_samples = 100000
    np.random.seed(42)
    
    # Generate random points in unit square
    x = np.random.uniform(-1, 1, n_samples)
    y = np.random.uniform(-1, 1, n_samples)
    
    # Check if points are inside unit circle
    inside_circle = (x**2 + y**2) <= 1
    pi_estimate = 4 * np.sum(inside_circle) / n_samples
    
    print(f"Estimating π using {n_samples} random points:")
    print(f"Points inside circle: {np.sum(inside_circle)}")
    print(f"Estimated π: {pi_estimate:.6f}")
    print(f"Actual π: {np.pi:.6f}")
    print(f"Error: {abs(pi_estimate - np.pi):.6f}")
    
    # Central Limit Theorem demonstration
    print(f"\nCentral Limit Theorem Demonstration:")
    sample_means = []
    for _ in range(1000):
        sample = np.random.exponential(scale=2, size=30)  # Exponential distribution
        sample_means.append(np.mean(sample))
    
    sample_means = np.array(sample_means)
    print(f"Mean of sample means: {np.mean(sample_means):.4f}")
    print(f"Std of sample means: {np.std(sample_means):.4f}")
    print(f"Theoretical std: {2/np.sqrt(30):.4f}")  # σ/√n where σ=2 for exponential

def main():
    """Main function to run all probability and statistics problems"""
    prob1, prob2, percentile_95 = solve_normal_distribution_problems()
    demonstrate_statistical_tests()
    demonstrate_other_distributions()
    monte_carlo_simulation()
    
    print(f"\n\nSummary of Normal Distribution Results:")
    print("=" * 45)
    print(f"P(X < 60) = {prob1:.4f}")
    print(f"P(40 < X < 70) = {prob2:.4f}")
    print(f"95th percentile = {percentile_95:.2f}")
    
    visualize_distributions()

if __name__ == "__main__":
    main()
