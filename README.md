# Mathematical Assignments - Complete Solutions

This repository contains complete implementations and solutions for mathematical assignments covering neural networks, linear algebra, calculus, and probability/statistics.

## 📁 Project Structure

```
├── Mathematical_Assignments_Solutions.md  # Detailed theoretical solutions
├── neural_network_modified.py            # Neural network with modified inputs
├── linear_algebra_operations.py          # Matrix operations and eigenvalues
├── calculus_problems.py                  # Optimization and integration
├── probability_statistics.py             # Statistical analysis
├── run_all_assignments.py               # Main script to run everything
├── requirements.txt                      # Python dependencies
└── README.md                            # This file
```

## 🎯 Assignment Overview

### 1. Neural Network with Modified Input Features
**Key Modification**: Input features are (x1, x2, x3, x1², x2²) instead of traditional (x1, x2, x3)

**Features:**
- Complete TensorFlow/Keras implementation
- Feature engineering with squared terms
- Training and evaluation pipeline
- Gradient computation analysis
- Visualization capabilities

### 2. Linear Algebra Operations
**Problems Solved:**
- Matrix multiplication and determinants
- Matrix inverse calculations
- Eigenvalues and eigenvectors
- Characteristic polynomial analysis

**Features:**
- Step-by-step mathematical solutions
- Numerical verification
- Advanced decompositions (SVD, QR)
- Visualization of transformations

### 3. Calculus Problems
**Problems Solved:**
- Optimization: Find minimum of f(x,y) = x² + y² - 2x - 4y + 5
- Integration: Evaluate ∫₀¹ ∫₀¹ xy e^(x+y) dx dy

**Features:**
- Analytical and numerical solutions
- Hessian matrix analysis
- 3D visualization of functions
- Symbolic computation with SymPy

### 4. Probability and Statistics
**Problems Solved:**
- Normal distribution analysis (μ=50, σ=10)
- P(X < 60), P(40 < X < 70), 95th percentile
- Statistical tests and confidence intervals

**Features:**
- Complete probability calculations
- Hypothesis testing
- Multiple distribution types
- Monte Carlo simulations

## 🚀 Quick Start

### Prerequisites
```bash
pip install -r requirements.txt
```

### Running the Solutions

#### Option 1: Run All Assignments
```bash
python run_all_assignments.py
```

#### Option 2: Run Individual Assignments
```bash
# Neural Network
python neural_network_modified.py

# Linear Algebra
python linear_algebra_operations.py

# Calculus
python calculus_problems.py

# Probability & Statistics
python probability_statistics.py
```

## 📊 Expected Results

### Neural Network Results
- Training accuracy: ~85-95%
- Modified input features successfully implemented
- Gradient computation verified

### Linear Algebra Results
```
Matrix A determinant: -15
Matrix AB multiplication: [[4, 10], [8, 7], [5, 2]]
Eigenvalues of C: [2, 3]
```

### Calculus Results
```
Optimization minimum: (1, 2) with value: 0
Integration result: 1.000000
```

### Statistics Results
```
P(X < 60) = 0.8413
P(40 < X < 70) = 0.8185
95th percentile = 66.45
```

## 🔧 Technical Details

### Neural Network Architecture
```
Input Layer: 5 neurons (x1, x2, x3, x1², x2²)
Hidden Layer: 8 neurons with ReLU activation
Output Layer: 1 neuron with sigmoid activation
```

### Key Mathematical Formulations

**Modified Neural Network Forward Pass:**
```
X = [x1, x2, x3, x1², x2²]ᵀ
h = σ(W₁X + b₁)
y = σ(W₂h + b₂)
```

**Gradient Computation:**
```
∂L/∂x1 = ∂L/∂h · ∂h/∂x1 + ∂L/∂h · ∂h/∂(x1²) · 2x1
∂L/∂x2 = ∂L/∂h · ∂h/∂x2 + ∂L/∂h · ∂h/∂(x2²) · 2x2
∂L/∂x3 = ∂L/∂h · ∂h/∂x3
```

## 📈 Visualizations

The code generates various visualizations:
- 3D surface plots for optimization problems
- Contour plots and gradient fields
- Eigenvalue/eigenvector visualizations
- Probability distribution plots
- Training history graphs

## 🧪 Testing and Validation

Each module includes:
- Numerical verification of analytical solutions
- Comparison with expected results
- Error checking and validation
- Step-by-step solution verification

## 📚 Dependencies

- **NumPy**: Numerical computations
- **SciPy**: Scientific computing and statistics
- **TensorFlow**: Neural network implementation
- **Matplotlib**: Plotting and visualization
- **SymPy**: Symbolic mathematics
- **Scikit-learn**: Machine learning utilities
- **Pandas**: Data manipulation

## 🎓 Educational Value

This project demonstrates:
- **Feature Engineering**: How squared terms enhance neural networks
- **Mathematical Rigor**: Step-by-step analytical solutions
- **Computational Methods**: Numerical verification of theory
- **Visualization**: Graphical understanding of concepts
- **Best Practices**: Clean, documented, modular code

## 🔍 Key Insights

1. **Neural Networks**: Adding squared terms (x1², x2²) introduces non-linearity that can improve pattern recognition
2. **Linear Algebra**: Matrix operations form the foundation of many ML algorithms
3. **Calculus**: Optimization techniques are crucial for training neural networks
4. **Statistics**: Understanding distributions is essential for data analysis

## 📝 Notes

- All solutions include both theoretical explanations and practical implementations
- Code is designed to be educational and well-documented
- Visualizations help understand mathematical concepts
- Error handling ensures robust execution

## 🤝 Usage

This code can be used for:
- Educational purposes and learning
- Reference implementations
- Assignment solutions
- Mathematical computation examples
- Neural network experimentation

---

**Author**: AI Assistant  
**Date**: 2025-09-28  
**Language**: Python 3.7+
