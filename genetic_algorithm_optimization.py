"""
Genetic Algorithm Optimization
Problem: Maximize and Minimize y = 2x₁² - 3x₂² - 4x₁ + 5x₂ + x₃
Variables: x₁, x₂, x₃ ∈ [0, 15] (each represented by 4-bit binary)
Population: 100 individuals
Generations: 100
"""

import numpy as np
import matplotlib.pyplot as plt
import random
from typing import List, Tuple

class GeneticAlgorithm:
    def __init__(self, population_size=100, generations=100, 
                 crossover_rate=0.8, mutation_rate=0.01, 
                 tournament_size=5, elite_size=2):
        """
        Initialize Genetic Algorithm parameters
        
        Args:
            population_size: Number of individuals in population
            generations: Number of generations to evolve
            crossover_rate: Probability of crossover
            mutation_rate: Probability of mutation per bit
            tournament_size: Size of tournament selection
            elite_size: Number of elite individuals to preserve
        """
        self.population_size = population_size
        self.generations = generations
        self.crossover_rate = crossover_rate
        self.mutation_rate = mutation_rate
        self.tournament_size = tournament_size
        self.elite_size = elite_size
        
        # Each variable (x1, x2, x3) uses 4 bits, total 12 bits per individual
        self.chromosome_length = 12
        self.bits_per_variable = 4
        
        # History tracking
        self.max_fitness_history = []
        self.min_fitness_history = []
        self.avg_fitness_history = []
        
    def objective_function(self, x1, x2, x3):
        """Objective function: y = 2x₁² - 3x₂² - 4x₁ + 5x₂ + x₃"""
        return 2 * x1**2 - 3 * x2**2 - 4 * x1 + 5 * x2 + x3
    
    def binary_to_decimal(self, binary_string):
        """Convert 4-bit binary string to decimal (0-15)"""
        return int(binary_string, 2)
    
    def decimal_to_binary(self, decimal_value):
        """Convert decimal (0-15) to 4-bit binary string"""
        return format(decimal_value, '04b')
    
    def decode_chromosome(self, chromosome):
        """Decode 12-bit chromosome to (x1, x2, x3) values"""
        x1_binary = chromosome[:4]
        x2_binary = chromosome[4:8]
        x3_binary = chromosome[8:12]
        
        x1 = self.binary_to_decimal(x1_binary)
        x2 = self.binary_to_decimal(x2_binary)
        x3 = self.binary_to_decimal(x3_binary)
        
        return x1, x2, x3
    
    def encode_chromosome(self, x1, x2, x3):
        """Encode (x1, x2, x3) values to 12-bit chromosome"""
        x1_binary = self.decimal_to_binary(x1)
        x2_binary = self.decimal_to_binary(x2)
        x3_binary = self.decimal_to_binary(x3)
        
        return x1_binary + x2_binary + x3_binary
    
    def calculate_fitness(self, chromosome, maximize=True):
        """Calculate fitness of a chromosome"""
        x1, x2, x3 = self.decode_chromosome(chromosome)
        objective_value = self.objective_function(x1, x2, x3)
        
        if maximize:
            return objective_value
        else:
            return -objective_value  # For minimization, negate the value
    
    def initialize_population(self):
        """Initialize random population"""
        population = []
        for _ in range(self.population_size):
            # Generate random chromosome (12 bits)
            chromosome = ''.join(random.choice('01') for _ in range(self.chromosome_length))
            population.append(chromosome)
        return population
    
    def tournament_selection(self, population, fitness_scores):
        """Tournament selection"""
        selected = []
        for _ in range(self.population_size - self.elite_size):
            # Select random individuals for tournament
            tournament_indices = random.sample(range(len(population)), self.tournament_size)
            tournament_fitness = [fitness_scores[i] for i in tournament_indices]
            
            # Select the best individual from tournament
            winner_index = tournament_indices[np.argmax(tournament_fitness)]
            selected.append(population[winner_index])
        
        return selected
    
    def single_point_crossover(self, parent1, parent2):
        """Single-point crossover"""
        if random.random() > self.crossover_rate:
            return parent1, parent2
        
        crossover_point = random.randint(1, self.chromosome_length - 1)
        
        child1 = parent1[:crossover_point] + parent2[crossover_point:]
        child2 = parent2[:crossover_point] + parent1[crossover_point:]
        
        return child1, child2
    
    def bit_flip_mutation(self, chromosome):
        """Bit-flip mutation"""
        mutated = list(chromosome)
        for i in range(len(mutated)):
            if random.random() < self.mutation_rate:
                mutated[i] = '1' if mutated[i] == '0' else '0'
        return ''.join(mutated)
    
    def elitism(self, population, fitness_scores):
        """Select elite individuals"""
        elite_indices = np.argsort(fitness_scores)[-self.elite_size:]
        elite = [population[i] for i in elite_indices]
        return elite
    
    def evolve(self, maximize=True):
        """Main evolution loop"""
        print(f"Starting Genetic Algorithm ({'Maximization' if maximize else 'Minimization'})")
        print(f"Population Size: {self.population_size}")
        print(f"Generations: {self.generations}")
        print(f"Crossover Rate: {self.crossover_rate}")
        print(f"Mutation Rate: {self.mutation_rate}")
        print(f"Tournament Size: {self.tournament_size}")
        print(f"Elite Size: {self.elite_size}")
        print("-" * 60)
        
        # Initialize population
        population = self.initialize_population()
        
        # Evolution loop
        for generation in range(self.generations):
            # Calculate fitness for all individuals
            fitness_scores = [self.calculate_fitness(chromosome, maximize) for chromosome in population]
            
            # Track statistics
            max_fitness = max(fitness_scores)
            min_fitness = min(fitness_scores)
            avg_fitness = np.mean(fitness_scores)
            
            self.max_fitness_history.append(max_fitness)
            self.min_fitness_history.append(min_fitness)
            self.avg_fitness_history.append(avg_fitness)
            
            # Print progress every 10 generations
            if (generation + 1) % 10 == 0:
                best_chromosome = population[np.argmax(fitness_scores)]
                x1, x2, x3 = self.decode_chromosome(best_chromosome)
                actual_value = self.objective_function(x1, x2, x3)
                print(f"Gen {generation+1:3d}: Best = {max_fitness:8.2f}, "
                      f"Avg = {avg_fitness:8.2f}, "
                      f"Variables = ({x1:2d}, {x2:2d}, {x3:2d}), "
                      f"Actual = {actual_value:8.2f}")
            
            # Selection
            elite = self.elitism(population, fitness_scores)
            selected = self.tournament_selection(population, fitness_scores)
            
            # Crossover
            new_population = elite.copy()
            for i in range(0, len(selected), 2):
                if i + 1 < len(selected):
                    parent1, parent2 = selected[i], selected[i + 1]
                    child1, child2 = self.single_point_crossover(parent1, parent2)
                    new_population.extend([child1, child2])
                else:
                    new_population.append(selected[i])
            
            # Ensure population size
            while len(new_population) < self.population_size:
                new_population.append(random.choice(selected))
            new_population = new_population[:self.population_size]
            
            # Mutation
            population = [self.bit_flip_mutation(chromosome) for chromosome in new_population]
        
        # Final results
        final_fitness_scores = [self.calculate_fitness(chromosome, maximize) for chromosome in population]
        best_index = np.argmax(final_fitness_scores)
        best_chromosome = population[best_index]
        best_fitness = final_fitness_scores[best_index]
        
        x1, x2, x3 = self.decode_chromosome(best_chromosome)
        actual_value = self.objective_function(x1, x2, x3)
        
        print("-" * 60)
        print(f"Final Results ({'Maximization' if maximize else 'Minimization'}):")
        print(f"Best Chromosome: {best_chromosome}")
        print(f"Best Variables: x₁={x1}, x₂={x2}, x₃={x3}")
        print(f"Best Fitness: {best_fitness:.4f}")
        print(f"Actual Function Value: {actual_value:.4f}")
        
        return {
            'best_chromosome': best_chromosome,
            'best_variables': (x1, x2, x3),
            'best_fitness': best_fitness,
            'actual_value': actual_value,
            'fitness_history': self.max_fitness_history.copy()
        }
    
    def plot_evolution(self, maximize_results, minimize_results):
        """Plot evolution progress for both maximization and minimization"""
        generations = range(1, self.generations + 1)
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        # Maximization results
        ax1.plot(generations, maximize_results['fitness_history'], 'r-', linewidth=2, label='Best Fitness')
        ax1.set_title('Maximization: Best Fitness Evolution', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Generation')
        ax1.set_ylabel('Fitness Value')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # Minimization results (convert back to actual function values)
        min_actual_values = [-f for f in minimize_results['fitness_history']]
        ax2.plot(generations, min_actual_values, 'b-', linewidth=2, label='Best Fitness')
        ax2.set_title('Minimization: Best Fitness Evolution', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Generation')
        ax2.set_ylabel('Function Value')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        # Combined comparison
        ax3.plot(generations, maximize_results['fitness_history'], 'r-', linewidth=2, label='Maximization')
        ax3.plot(generations, min_actual_values, 'b-', linewidth=2, label='Minimization')
        ax3.set_title('Comparison: Maximization vs Minimization', fontsize=14, fontweight='bold')
        ax3.set_xlabel('Generation')
        ax3.set_ylabel('Function Value')
        ax3.grid(True, alpha=0.3)
        ax3.legend()
        
        # Convergence analysis
        max_convergence = np.array(maximize_results['fitness_history'])
        min_convergence = np.array(min_actual_values)
        
        ax4.plot(generations, max_convergence - max_convergence[0], 'r-', linewidth=2, label='Max Improvement')
        ax4.plot(generations, min_convergence - min_convergence[0], 'b-', linewidth=2, label='Min Improvement')
        ax4.set_title('Convergence Analysis (Improvement from Initial)', fontsize=14, fontweight='bold')
        ax4.set_xlabel('Generation')
        ax4.set_ylabel('Improvement')
        ax4.grid(True, alpha=0.3)
        ax4.legend()
        
        plt.tight_layout()
        plt.savefig('genetic_algorithm_evolution.png', dpi=300, bbox_inches='tight')
        plt.show()

def main():
    """Main function to run genetic algorithm optimization"""
    print("Genetic Algorithm Optimization")
    print("=" * 60)
    print("Function: y = 2x₁² - 3x₂² - 4x₁ + 5x₂ + x₃")
    print("Variables: x₁, x₂, x₃ ∈ [0, 15]")
    print("Encoding: 4-bit binary per variable (12 bits total)")
    print("=" * 60)
    
    # Create GA instance
    ga = GeneticAlgorithm(
        population_size=100,
        generations=100,
        crossover_rate=0.8,
        mutation_rate=0.01,
        tournament_size=5,
        elite_size=2
    )
    
    # Run maximization
    print("\n" + "=" * 60)
    print("MAXIMIZATION")
    print("=" * 60)
    maximize_results = ga.evolve(maximize=True)
    
    # Reset history for minimization
    ga.max_fitness_history = []
    ga.min_fitness_history = []
    ga.avg_fitness_history = []
    
    # Run minimization
    print("\n" + "=" * 60)
    print("MINIMIZATION")
    print("=" * 60)
    minimize_results = ga.evolve(maximize=False)
    
    # Plot results
    ga.plot_evolution(maximize_results, minimize_results)
    
    # Summary
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    print("Maximization:")
    print(f"  Best Solution: x₁={maximize_results['best_variables'][0]}, "
          f"x₂={maximize_results['best_variables'][1]}, "
          f"x₃={maximize_results['best_variables'][2]}")
    print(f"  Maximum Value: {maximize_results['actual_value']:.4f}")
    
    print("\nMinimization:")
    print(f"  Best Solution: x₁={minimize_results['best_variables'][0]}, "
          f"x₂={minimize_results['best_variables'][1]}, "
          f"x₃={minimize_results['best_variables'][2]}")
    print(f"  Minimum Value: {minimize_results['actual_value']:.4f}")
    
    return maximize_results, minimize_results

if __name__ == "__main__":
    max_results, min_results = main()
