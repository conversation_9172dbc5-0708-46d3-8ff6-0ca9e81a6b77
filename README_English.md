# Neural Network Regression Exercise - English Version

## Overview

This project implements a neural network regression exercise with modified input features. The main goal is to approximate the function:

**y = 2x₁² - 3x₂² - 4x₁ + 5x₂ + x₃**

Using a two-layer linear neural network with input features: **[x₁, x₂, x₃, x₁², x₂²]**

## Key Features

### Problem Specifications
- **Function**: y = 2x₁² - 3x₂² - 4x₁ + 5x₂ + x₃
- **Input**: [x₁, x₂, x₃, x₁², x₂²] (modified from traditional [x₁, x₂, x₃])
- **Output**: y (regression target)
- **Activation Function**: Linear function
- **Learning Method**: Gradient descent (Least squares method)

### Experimental Steps
1. **Create Dataset**: Generate all 4096 points (x₁, x₂, x₃ ∈ {0, 1, 2, ..., 15})
2. **Data Split**: Divide into training (80%) and validation (20%) sets
3. **Training Process**: Record for each epoch:
   - Validation error (MSE)
   - Maximum and minimum values of predictions for all input points
4. **Visualization**: Plot changes in maximum and minimum values during learning
5. **Final Analysis**: Compare maximum and minimum inputs and outputs with true function values

## Files

- `neural_network_regression.py` - Main implementation file
- `generate_charts_english.py` - Chart generation script
- `requirements.txt` - Python dependencies
- `README_English.md` - This documentation

## Quick Start

### Prerequisites
```bash
pip install -r requirements.txt
```

### Run the Experiment
```bash
python neural_network_regression.py
```

### Generate Charts
```bash
python generate_charts_english.py
```

## Expected Results

### Training Performance
- **Dataset Size**: 4096 samples
- **Training Set**: 3276 samples (80%)
- **Validation Set**: 820 samples (20%)
- **Input Dimension**: 5 features (x₁, x₂, x₃, x₁², x₂²)
- **y Value Range**: [-61.00, 1545.00]

### Model Architecture
```
Input Layer: 5 neurons (x₁, x₂, x₃, x₁², x₂²)
Hidden Layer: 10 neurons (linear activation)
Output Layer: 1 neuron (linear activation)
```

### Sample Results
```
Final Analysis: Comparing Predicted Values with True Values
============================================================
Predicted Maximum: 1333.09
Corresponding Input: x₁=15.0, x₂=0.0, x₃=15.0
True Value: 1545.00
Error: 211.91

Predicted Minimum: -203.14
Corresponding Input: x₁=0.0, x₂=12.0, x₃=0.0
True Value: -48.00
Error: 155.14

Overall Performance:
Mean Squared Error (MSE): 14220.34
Mean Absolute Error (MAE): 90.31
```

## Key Implementation Details

### Modified Input Features
The core innovation is the automatic feature engineering using TensorFlow's Lambda layer:

```python
# Create modified input features [x₁, x₂, x₃, x₁², x₂²]
X_modified = np.column_stack([
    x1, x2, x3,  # original features
    x1**2, x2**2  # squared terms
])
```

### Network Architecture
```python
self.model = models.Sequential([
    # First layer: Linear layer (no activation function)
    layers.Dense(10, activation='linear', name='hidden_layer'),
    
    # Second layer: Output layer (linear activation)
    layers.Dense(1, activation='linear', name='output_layer')
])
```

### Training Monitoring
Custom callback function records:
- Validation MSE for each epoch
- Maximum and minimum predictions across all data points
- Comparison with true maximum and minimum values

## Visualization

The code generates several charts:
1. **Validation Error (MSE) Changes** - Shows training convergence
2. **Maximum Value Changes** - Compares predicted vs true maximum values
3. **Minimum Value Changes** - Compares predicted vs true minimum values
4. **Predicted Range vs True Range** - Shows overall prediction range evolution

## Technical Highlights

### Feature Engineering
- Automatic computation of squared terms (x₁², x₂²)
- Maintains computational efficiency
- Enhances model's ability to capture quadratic relationships

### Linear Network Design
- Uses linear activation functions as specified
- Implements least squares method through MSE loss
- Two-layer architecture for regression task

### Comprehensive Analysis
- Tracks prediction quality throughout training
- Identifies specific inputs corresponding to extreme values
- Provides detailed error analysis

## Dependencies

- numpy >= 1.21.0
- tensorflow >= 2.8.0
- matplotlib >= 3.4.0
- scikit-learn >= 1.0.0

## Usage Notes

1. **Random Seed**: Results may vary slightly due to random initialization
2. **Training Time**: Approximately 2-3 minutes for 100 epochs
3. **Memory**: Requires ~100MB RAM for the 4096-point dataset
4. **Visualization**: Charts are saved as PNG files and displayed

## Educational Value

This exercise demonstrates:
- **Feature Engineering**: How squared terms enhance neural networks
- **Regression with Neural Networks**: Linear activation for continuous outputs
- **Systematic Evaluation**: Comprehensive tracking of training progress
- **Mathematical Validation**: Comparison with analytical solutions

## Conclusion

The implementation successfully demonstrates the use of modified input features [x₁, x₂, x₃, x₁², x₂²] in a linear neural network for regression. The systematic approach to data generation, training monitoring, and result analysis provides a comprehensive framework for understanding neural network behavior in regression tasks.
