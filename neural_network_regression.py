"""
Neural Network Regression Exercise Implementation
Problem: y = 2x₁² - 3x₁ - 4x₂ + 5x₁x₃ + x₃
Using two-layer linear neural network as regression model
Input: [x₁, x₂, x₃, x₁², x₂²]
Output: y
Activation function: Linear function
Learning method: Gradient descent (Least squares method)
"""

import numpy as np
import tensorflow as tf
from tensorflow.keras import layers, models
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
import itertools

class LinearRegressionNN:
    def __init__(self):
        """Initialize linear regression neural network"""
        self.model = None
        self.history = {
            'epoch': [],
            'val_mse': [],
            'max_pred': [],
            'min_pred': [],
            'max_true': [],
            'min_true': []
        }

    def true_function(self, x1, x2, x3):
        """True function: y = 2x₁² - 3x₁ - 4x₂ + 5x₁x₃ + x₃"""
        return 2 * x1**2 - 3 * x1 - 4 * x2 + 5 * x1 * x3 + x3

    def create_dataset(self):
        """Create dataset: Generate all 4096 points (16³ = 4096)"""
        print("Creating dataset: Generating all 4096 points...")

        # Generate all possible combinations x₁, x₂, x₃ ∈ {0, 1, 2, ..., 15}
        x_values = list(range(16))
        all_combinations = list(itertools.product(x_values, repeat=3))

        # Convert to numpy array
        X_original = np.array(all_combinations, dtype=np.float32)
        x1, x2, x3 = X_original[:, 0], X_original[:, 1], X_original[:, 2]

        # Calculate true y values
        y = self.true_function(x1, x2, x3)

        # Create modified input features [x₁, x₂, x₃, x₁², x₂²]
        X_modified = np.column_stack([
            x1, x2, x3,  # original features
            x1**2, x2**2  # squared terms
        ])

        print(f"Dataset size: {X_modified.shape[0]} samples")
        print(f"Input feature dimension: {X_modified.shape[1]} (x₁, x₂, x₃, x₁², x₂²)")
        print(f"y value range: [{y.min():.2f}, {y.max():.2f}]")

        return X_modified, y, X_original
    
    def create_model(self, input_dim=5):
        """Create two-layer linear neural network"""
        self.model = models.Sequential([
            # First layer: Linear layer (no activation function)
            layers.Dense(10, activation='linear', name='hidden_layer'),

            # Second layer: Output layer (linear activation)
            layers.Dense(1, activation='linear', name='output_layer')
        ])

        # Compile model: Use MSE loss function (least squares method)
        self.model.compile(
            optimizer='adam',
            loss='mse',  # Mean squared error (least squares method)
            metrics=['mae']
        )

        return self.model
    
    def train_model(self, X_train, y_train, X_val, y_val, X_all, y_all, epochs=100):
        """Train model and record information for each epoch"""
        print(f"\nStarting model training ({epochs} epochs)...")

        # Custom callback function to record information for each epoch
        class CustomCallback(tf.keras.callbacks.Callback):
            def __init__(self, model, X_val, y_val, X_all, y_all, history_dict, true_func):
                self.X_val = X_val
                self.y_val = y_val
                self.X_all = X_all
                self.y_all = y_all
                self.history_dict = history_dict
                self.true_func = true_func

            def on_epoch_end(self, epoch, logs=None):
                # 1. Validation error (MSE)
                val_mse = logs.get('val_loss', 0)

                # 2. Maximum and minimum values of predictions for all input points
                all_predictions = self.model.predict(self.X_all, verbose=0)
                max_pred = np.max(all_predictions)
                min_pred = np.min(all_predictions)

                # Maximum and minimum values of true values
                max_true = np.max(self.y_all)
                min_true = np.min(self.y_all)

                # Record history
                self.history_dict['epoch'].append(epoch + 1)
                self.history_dict['val_mse'].append(val_mse)
                self.history_dict['max_pred'].append(max_pred)
                self.history_dict['min_pred'].append(min_pred)
                self.history_dict['max_true'].append(max_true)
                self.history_dict['min_true'].append(min_true)

                if (epoch + 1) % 10 == 0:
                    print(f"Epoch {epoch+1}: Val MSE={val_mse:.4f}, "
                          f"Pred Range=[{min_pred:.2f}, {max_pred:.2f}], "
                          f"True Range=[{min_true:.2f}, {max_true:.2f}]")

        # Create callback function
        custom_callback = CustomCallback(
            self.model, X_val, y_val, X_all, y_all, self.history, self.true_function
        )

        # Train model
        history = self.model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=epochs,
            batch_size=32,
            verbose=0,  # Reduce output
            callbacks=[custom_callback]
        )

        return history
    
    def plot_training_progress(self):
        """Plot charts showing changes in maximum and minimum values during learning process"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

        epochs = self.history['epoch']

        # 1. Validation MSE changes
        ax1.plot(epochs, self.history['val_mse'], 'b-', linewidth=2)
        ax1.set_title('Validation Error (MSE) Changes', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('MSE')
        ax1.grid(True, alpha=0.3)

        # 2. Maximum value changes
        ax2.plot(epochs, self.history['max_pred'], 'r-', label='Predicted Maximum', linewidth=2)
        ax2.plot(epochs, self.history['max_true'], 'r--', label='True Maximum', linewidth=2)
        ax2.set_title('Maximum Value Changes', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Maximum Value')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. Minimum value changes
        ax3.plot(epochs, self.history['min_pred'], 'g-', label='Predicted Minimum', linewidth=2)
        ax3.plot(epochs, self.history['min_true'], 'g--', label='True Minimum', linewidth=2)
        ax3.set_title('Minimum Value Changes', fontsize=14, fontweight='bold')
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('Minimum Value')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. Predicted range vs true range
        ax4.fill_between(epochs, self.history['min_pred'], self.history['max_pred'],
                        alpha=0.3, color='blue', label='Predicted Range')
        ax4.fill_between(epochs, self.history['min_true'], self.history['max_true'],
                        alpha=0.3, color='red', label='True Range')
        ax4.plot(epochs, self.history['max_pred'], 'b-', linewidth=1)
        ax4.plot(epochs, self.history['min_pred'], 'b-', linewidth=1)
        ax4.plot(epochs, self.history['max_true'], 'r--', linewidth=1)
        ax4.plot(epochs, self.history['min_true'], 'r--', linewidth=1)
        ax4.set_title('Predicted Range vs True Range', fontsize=14, fontweight='bold')
        ax4.set_xlabel('Epoch')
        ax4.set_ylabel('Value')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('neural_network_training_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def final_analysis(self, X_all, y_all, X_original):
        """Final analysis: Compare maximum and minimum inputs and their output values with true function values"""
        print("\n" + "="*60)
        print("Final Analysis: Comparing Predicted Values with True Values")
        print("="*60)

        # Get all predictions
        predictions = self.model.predict(X_all, verbose=0).flatten()

        # Find indices of maximum and minimum values
        max_pred_idx = np.argmax(predictions)
        min_pred_idx = np.argmin(predictions)
        max_true_idx = np.argmax(y_all)
        min_true_idx = np.argmin(y_all)

        print("Predicted Value Analysis:")
        print(f"Predicted Maximum: {predictions[max_pred_idx]:.4f}")
        print(f"Corresponding Input: x₁={X_original[max_pred_idx, 0]}, x₂={X_original[max_pred_idx, 1]}, x₃={X_original[max_pred_idx, 2]}")
        print(f"True Value: {y_all[max_pred_idx]:.4f}")
        print(f"Error: {abs(predictions[max_pred_idx] - y_all[max_pred_idx]):.4f}")

        print(f"\nPredicted Minimum: {predictions[min_pred_idx]:.4f}")
        print(f"Corresponding Input: x₁={X_original[min_pred_idx, 0]}, x₂={X_original[min_pred_idx, 1]}, x₃={X_original[min_pred_idx, 2]}")
        print(f"True Value: {y_all[min_pred_idx]:.4f}")
        print(f"Error: {abs(predictions[min_pred_idx] - y_all[min_pred_idx]):.4f}")

        print("\nTrue Value Analysis:")
        print(f"True Maximum: {y_all[max_true_idx]:.4f}")
        print(f"Corresponding Input: x₁={X_original[max_true_idx, 0]}, x₂={X_original[max_true_idx, 1]}, x₃={X_original[max_true_idx, 2]}")
        print(f"Predicted Value: {predictions[max_true_idx]:.4f}")
        print(f"Error: {abs(predictions[max_true_idx] - y_all[max_true_idx]):.4f}")

        print(f"\nTrue Minimum: {y_all[min_true_idx]:.4f}")
        print(f"Corresponding Input: x₁={X_original[min_true_idx, 0]}, x₂={X_original[min_true_idx, 1]}, x₃={X_original[min_true_idx, 2]}")
        print(f"Predicted Value: {predictions[min_true_idx]:.4f}")
        print(f"Error: {abs(predictions[min_true_idx] - y_all[min_true_idx]):.4f}")

        # Overall performance
        mse = np.mean((predictions - y_all)**2)
        mae = np.mean(np.abs(predictions - y_all))
        print(f"\nOverall Performance:")
        print(f"Mean Squared Error (MSE): {mse:.4f}")
        print(f"Mean Absolute Error (MAE): {mae:.4f}")
        
        return {
            'max_pred_idx': max_pred_idx,
            'min_pred_idx': min_pred_idx,
            'max_true_idx': max_true_idx,
            'min_true_idx': min_true_idx,
            'mse': mse,
            'mae': mae
        }

def main():
    """Main function: Execute complete experimental procedure"""
    print("Neural Network Regression Exercise")
    print("="*50)
    print("Function: y = 2x₁² - 3x₁ - 4x₂ + 5x₁x₃ + x₃")
    print("Input: [x₁, x₂, x₃, x₁², x₂²]")
    print("Network: Two-layer linear neural network")
    print("="*50)

    # Create neural network instance
    nn = LinearRegressionNN()

    # 1. Create dataset
    X_modified, y, X_original = nn.create_dataset()

    # 2. Split training and validation sets (80%/20%)
    X_train, X_val, y_train, y_val = train_test_split(
        X_modified, y, test_size=0.2, random_state=42
    )
    print(f"\nData Split:")
    print(f"Training set: {X_train.shape[0]} samples (80%)")
    print(f"Validation set: {X_val.shape[0]} samples (20%)")

    # 3. Create and train model
    model = nn.create_model()
    print(f"\nModel Architecture:")
    model.summary()

    # 4. Train model
    history = nn.train_model(X_train, y_train, X_val, y_val, X_modified, y, epochs=100)

    # 5. Plot training process charts
    nn.plot_training_progress()

    # 6. Final analysis
    results = nn.final_analysis(X_modified, y, X_original)

    print("\nExperiment completed!")
    return nn, results

if __name__ == "__main__":
    nn, results = main()
