"""
神经网络回归练习题实现
题目：y = 2x₁² - 3x₁ - 4x₂ + 5x₁x₃ + x₃
使用两层线性神经网络作为回归模型
输入：[x₁, x₂, x₃, x₁², x₂²]
输出：y
激活函数：线性函数
学习方法：梯度下降法（最小二乘法）
"""

import numpy as np
import tensorflow as tf
from tensorflow.keras import layers, models
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
import itertools

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class LinearRegressionNN:
    def __init__(self):
        """初始化线性回归神经网络"""
        self.model = None
        self.history = {
            'epoch': [],
            'val_mse': [],
            'max_pred': [],
            'min_pred': [],
            'max_true': [],
            'min_true': []
        }
        
    def true_function(self, x1, x2, x3):
        """真实函数：y = 2x₁² - 3x₁ - 4x₂ + 5x₁x₃ + x₃"""
        return 2 * x1**2 - 3 * x1 - 4 * x2 + 5 * x1 * x3 + x3
    
    def create_dataset(self):
        """创建数据集：生成全部4096个点 (16³ = 4096)"""
        print("创建数据集：生成全部4096个点...")
        
        # 生成所有可能的组合 x₁, x₂, x₃ ∈ {0, 1, 2, ..., 15}
        x_values = list(range(16))
        all_combinations = list(itertools.product(x_values, repeat=3))
        
        # 转换为numpy数组
        X_original = np.array(all_combinations, dtype=np.float32)
        x1, x2, x3 = X_original[:, 0], X_original[:, 1], X_original[:, 2]
        
        # 计算真实的y值
        y = self.true_function(x1, x2, x3)
        
        # 创建修改后的输入特征 [x₁, x₂, x₃, x₁², x₂²]
        X_modified = np.column_stack([
            x1, x2, x3,  # 原始特征
            x1**2, x2**2  # 平方项
        ])
        
        print(f"数据集大小：{X_modified.shape[0]} 个样本")
        print(f"输入特征维度：{X_modified.shape[1]} (x₁, x₂, x₃, x₁², x₂²)")
        print(f"y值范围：[{y.min():.2f}, {y.max():.2f}]")
        
        return X_modified, y, X_original
    
    def create_model(self, input_dim=5):
        """创建两层线性神经网络"""
        self.model = models.Sequential([
            # 第一层：线性层（无激活函数）
            layers.Dense(10, activation='linear', name='hidden_layer'),
            
            # 第二层：输出层（线性激活）
            layers.Dense(1, activation='linear', name='output_layer')
        ])
        
        # 编译模型：使用MSE损失函数（最小二乘法）
        self.model.compile(
            optimizer='adam',
            loss='mse',  # 均方误差（最小二乘法）
            metrics=['mae']
        )
        
        return self.model
    
    def train_model(self, X_train, y_train, X_val, y_val, X_all, y_all, epochs=100):
        """训练模型并记录每个epoch的信息"""
        print(f"\n开始训练模型（{epochs}个epoch）...")
        
        # 自定义回调函数来记录每个epoch的信息
        class CustomCallback(tf.keras.callbacks.Callback):
            def __init__(self, model, X_val, y_val, X_all, y_all, history_dict, true_func):
                self.X_val = X_val
                self.y_val = y_val
                self.X_all = X_all
                self.y_all = y_all
                self.history_dict = history_dict
                self.true_func = true_func
                
            def on_epoch_end(self, epoch, logs=None):
                # 1. 验证误差(MSE)
                val_mse = logs.get('val_loss', 0)
                
                # 2. 对所有输入点的预测值的最大值和最小值
                all_predictions = self.model.predict(self.X_all, verbose=0)
                max_pred = np.max(all_predictions)
                min_pred = np.min(all_predictions)
                
                # 真实值的最大值和最小值
                max_true = np.max(self.y_all)
                min_true = np.min(self.y_all)
                
                # 记录历史
                self.history_dict['epoch'].append(epoch + 1)
                self.history_dict['val_mse'].append(val_mse)
                self.history_dict['max_pred'].append(max_pred)
                self.history_dict['min_pred'].append(min_pred)
                self.history_dict['max_true'].append(max_true)
                self.history_dict['min_true'].append(min_true)
                
                if (epoch + 1) % 10 == 0:
                    print(f"Epoch {epoch+1}: Val MSE={val_mse:.4f}, "
                          f"Pred范围=[{min_pred:.2f}, {max_pred:.2f}], "
                          f"True范围=[{min_true:.2f}, {max_true:.2f}]")
        
        # 创建回调函数
        custom_callback = CustomCallback(
            self.model, X_val, y_val, X_all, y_all, self.history, self.true_function
        )
        
        # 训练模型
        history = self.model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=epochs,
            batch_size=32,
            verbose=0,  # 减少输出
            callbacks=[custom_callback]
        )
        
        return history
    
    def plot_training_progress(self):
        """绘制学习过程中最大值和最小值的变化图表"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        epochs = self.history['epoch']
        
        # 1. 验证MSE变化
        ax1.plot(epochs, self.history['val_mse'], 'b-', linewidth=2)
        ax1.set_title('验证误差(MSE)变化', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('MSE')
        ax1.grid(True, alpha=0.3)
        
        # 2. 预测值最大值变化
        ax2.plot(epochs, self.history['max_pred'], 'r-', label='预测最大值', linewidth=2)
        ax2.plot(epochs, self.history['max_true'], 'r--', label='真实最大值', linewidth=2)
        ax2.set_title('最大值变化', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('最大值')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 预测值最小值变化
        ax3.plot(epochs, self.history['min_pred'], 'g-', label='预测最小值', linewidth=2)
        ax3.plot(epochs, self.history['min_true'], 'g--', label='真实最小值', linewidth=2)
        ax3.set_title('最小值变化', fontsize=14, fontweight='bold')
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('最小值')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 预测范围vs真实范围
        ax4.fill_between(epochs, self.history['min_pred'], self.history['max_pred'], 
                        alpha=0.3, color='blue', label='预测范围')
        ax4.fill_between(epochs, self.history['min_true'], self.history['max_true'], 
                        alpha=0.3, color='red', label='真实范围')
        ax4.plot(epochs, self.history['max_pred'], 'b-', linewidth=1)
        ax4.plot(epochs, self.history['min_pred'], 'b-', linewidth=1)
        ax4.plot(epochs, self.history['max_true'], 'r--', linewidth=1)
        ax4.plot(epochs, self.history['min_true'], 'r--', linewidth=1)
        ax4.set_title('预测范围 vs 真实范围', fontsize=14, fontweight='bold')
        ax4.set_xlabel('Epoch')
        ax4.set_ylabel('值')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('神经网络训练过程分析.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def final_analysis(self, X_all, y_all, X_original):
        """最终分析：比较最大和最小输入及其输出值与真实函数值"""
        print("\n" + "="*60)
        print("最终分析：比较预测值与真实值")
        print("="*60)
        
        # 获取所有预测值
        predictions = self.model.predict(X_all, verbose=0).flatten()
        
        # 找到最大值和最小值的索引
        max_pred_idx = np.argmax(predictions)
        min_pred_idx = np.argmin(predictions)
        max_true_idx = np.argmax(y_all)
        min_true_idx = np.argmin(y_all)
        
        print("预测值分析：")
        print(f"预测最大值：{predictions[max_pred_idx]:.4f}")
        print(f"对应输入：x₁={X_original[max_pred_idx, 0]}, x₂={X_original[max_pred_idx, 1]}, x₃={X_original[max_pred_idx, 2]}")
        print(f"真实值：{y_all[max_pred_idx]:.4f}")
        print(f"误差：{abs(predictions[max_pred_idx] - y_all[max_pred_idx]):.4f}")
        
        print(f"\n预测最小值：{predictions[min_pred_idx]:.4f}")
        print(f"对应输入：x₁={X_original[min_pred_idx, 0]}, x₂={X_original[min_pred_idx, 1]}, x₃={X_original[min_pred_idx, 2]}")
        print(f"真实值：{y_all[min_pred_idx]:.4f}")
        print(f"误差：{abs(predictions[min_pred_idx] - y_all[min_pred_idx]):.4f}")
        
        print("\n真实值分析：")
        print(f"真实最大值：{y_all[max_true_idx]:.4f}")
        print(f"对应输入：x₁={X_original[max_true_idx, 0]}, x₂={X_original[max_true_idx, 1]}, x₃={X_original[max_true_idx, 2]}")
        print(f"预测值：{predictions[max_true_idx]:.4f}")
        print(f"误差：{abs(predictions[max_true_idx] - y_all[max_true_idx]):.4f}")
        
        print(f"\n真实最小值：{y_all[min_true_idx]:.4f}")
        print(f"对应输入：x₁={X_original[min_true_idx, 0]}, x₂={X_original[min_true_idx, 1]}, x₃={X_original[min_true_idx, 2]}")
        print(f"预测值：{predictions[min_true_idx]:.4f}")
        print(f"误差：{abs(predictions[min_true_idx] - y_all[min_true_idx]):.4f}")
        
        # 整体性能
        mse = np.mean((predictions - y_all)**2)
        mae = np.mean(np.abs(predictions - y_all))
        print(f"\n整体性能：")
        print(f"均方误差(MSE)：{mse:.4f}")
        print(f"平均绝对误差(MAE)：{mae:.4f}")
        
        return {
            'max_pred_idx': max_pred_idx,
            'min_pred_idx': min_pred_idx,
            'max_true_idx': max_true_idx,
            'min_true_idx': min_true_idx,
            'mse': mse,
            'mae': mae
        }

def main():
    """主函数：执行完整的实验流程"""
    print("神经网络回归练习题")
    print("="*50)
    print("函数：y = 2x₁² - 3x₁ - 4x₂ + 5x₁x₃ + x₃")
    print("输入：[x₁, x₂, x₃, x₁², x₂²]")
    print("网络：两层线性神经网络")
    print("="*50)
    
    # 创建神经网络实例
    nn = LinearRegressionNN()
    
    # 1. 创建数据集
    X_modified, y, X_original = nn.create_dataset()
    
    # 2. 划分训练集和验证集 (80%/20%)
    X_train, X_val, y_train, y_val = train_test_split(
        X_modified, y, test_size=0.2, random_state=42
    )
    print(f"\n数据划分：")
    print(f"训练集：{X_train.shape[0]} 个样本 (80%)")
    print(f"验证集：{X_val.shape[0]} 个样本 (20%)")
    
    # 3. 创建和训练模型
    model = nn.create_model()
    print(f"\n模型架构：")
    model.summary()
    
    # 4. 训练模型
    history = nn.train_model(X_train, y_train, X_val, y_val, X_modified, y, epochs=100)
    
    # 5. 绘制训练过程图表
    nn.plot_training_progress()
    
    # 6. 最终分析
    results = nn.final_analysis(X_modified, y, X_original)
    
    print("\n实验完成！")
    return nn, results

if __name__ == "__main__":
    nn, results = main()
