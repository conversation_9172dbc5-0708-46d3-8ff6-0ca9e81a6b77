"""
Neural Network with Modified Input Features
Implementation of neural network where input is (x1, x2, x3, x1², x2²)
"""

import numpy as np
import tensorflow as tf
from tensorflow.keras import layers, models
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler

class ModifiedNeuralNetwork:
    def __init__(self, input_dim=3, hidden_units=8, output_units=1, activation='relu'):
        """
        Initialize the modified neural network
        
        Args:
            input_dim: Original input dimension (default: 3 for x1, x2, x3)
            hidden_units: Number of hidden layer neurons
            output_units: Number of output neurons
            activation: Activation function for hidden layer
        """
        self.input_dim = input_dim
        self.hidden_units = hidden_units
        self.output_units = output_units
        self.activation = activation
        self.model = None
        self.scaler = StandardScaler()
        
    def create_model(self):
        """Create the neural network model with modified input features"""
        self.model = models.Sequential([
            # Input preprocessing layer to add squared terms
            layers.Lambda(lambda x: tf.concat([
                x,  # original features [x1, x2, x3]
                tf.square(x[:, :2])  # squared terms [x1², x2²]
            ], axis=1), name='feature_engineering'),
            
            # Hidden layer
            layers.Dense(self.hidden_units, activation=self.activation, name='hidden_layer'),
            layers.Dropout(0.2, name='dropout'),
            
            # Output layer
            layers.Dense(self.output_units, activation='sigmoid', name='output_layer')
        ])
        
        return self.model
    
    def compile_model(self, optimizer='adam', loss='binary_crossentropy', metrics=['accuracy']):
        """Compile the model"""
        if self.model is None:
            self.create_model()
        
        self.model.compile(optimizer=optimizer, loss=loss, metrics=metrics)
        
    def preprocess_data(self, X, fit_scaler=True):
        """Preprocess input data"""
        if fit_scaler:
            X_scaled = self.scaler.fit_transform(X)
        else:
            X_scaled = self.scaler.transform(X)
        return X_scaled
    
    def train(self, X, y, validation_split=0.2, epochs=100, batch_size=32, verbose=1):
        """Train the neural network"""
        if self.model is None:
            self.compile_model()
        
        # Preprocess data
        X_processed = self.preprocess_data(X, fit_scaler=True)
        
        # Train the model
        history = self.model.fit(
            X_processed, y,
            validation_split=validation_split,
            epochs=epochs,
            batch_size=batch_size,
            verbose=verbose
        )
        
        return history
    
    def predict(self, X):
        """Make predictions"""
        X_processed = self.preprocess_data(X, fit_scaler=False)
        return self.model.predict(X_processed)
    
    def evaluate(self, X, y):
        """Evaluate the model"""
        X_processed = self.preprocess_data(X, fit_scaler=False)
        return self.model.evaluate(X_processed, y)
    
    def plot_training_history(self, history):
        """Plot training history"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
        
        # Plot training & validation accuracy
        ax1.plot(history.history['accuracy'], label='Training Accuracy')
        ax1.plot(history.history['val_accuracy'], label='Validation Accuracy')
        ax1.set_title('Model Accuracy')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Accuracy')
        ax1.legend()
        
        # Plot training & validation loss
        ax2.plot(history.history['loss'], label='Training Loss')
        ax2.plot(history.history['val_loss'], label='Validation Loss')
        ax2.set_title('Model Loss')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Loss')
        ax2.legend()
        
        plt.tight_layout()
        plt.show()

def generate_sample_data(n_samples=1000, n_features=3, noise=0.1):
    """Generate sample data for testing"""
    np.random.seed(42)
    
    # Generate random input features
    X = np.random.randn(n_samples, n_features)
    
    # Create a non-linear relationship for y
    # y depends on x1, x2, x3 and their squares
    y = (X[:, 0]**2 + X[:, 1]**2 + 0.5*X[:, 2] + 
         0.3*X[:, 0]*X[:, 1] + noise*np.random.randn(n_samples))
    
    # Convert to binary classification
    y = (y > np.median(y)).astype(int)
    
    return X, y

def demonstrate_gradient_computation():
    """Demonstrate gradient computation for the modified input"""
    print("Gradient Computation for Modified Input Features:")
    print("=" * 50)
    print("For input features [x1, x2, x3, x1², x2²]:")
    print("∂L/∂x1 = ∂L/∂h · ∂h/∂x1 + ∂L/∂h · ∂h/∂(x1²) · 2x1")
    print("∂L/∂x2 = ∂L/∂h · ∂h/∂x2 + ∂L/∂h · ∂h/∂(x2²) · 2x2")
    print("∂L/∂x3 = ∂L/∂h · ∂h/∂x3")
    print("\nThis shows how the squared terms contribute additional gradient components.")

def main():
    """Main function to demonstrate the modified neural network"""
    print("Modified Neural Network Implementation")
    print("=" * 40)
    
    # Generate sample data
    print("Generating sample data...")
    X, y = generate_sample_data(n_samples=1000, n_features=3)
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42
    )
    
    print(f"Training data shape: {X_train.shape}")
    print(f"Test data shape: {X_test.shape}")
    
    # Create and train the model
    print("\nCreating and training the modified neural network...")
    nn = ModifiedNeuralNetwork(input_dim=3, hidden_units=8, output_units=1)
    
    # Train the model
    history = nn.train(X_train, y_train, epochs=50, batch_size=32, verbose=1)
    
    # Evaluate the model
    print("\nEvaluating the model...")
    train_loss, train_acc = nn.evaluate(X_train, y_train)
    test_loss, test_acc = nn.evaluate(X_test, y_test)
    
    print(f"Training Accuracy: {train_acc:.4f}")
    print(f"Test Accuracy: {test_acc:.4f}")
    
    # Make predictions
    print("\nMaking predictions on test data...")
    predictions = nn.predict(X_test[:5])
    print("Sample predictions:")
    for i, pred in enumerate(predictions):
        print(f"Sample {i+1}: {pred[0]:.4f} (Actual: {y_test[i]})")
    
    # Demonstrate gradient computation
    print("\n")
    demonstrate_gradient_computation()
    
    # Plot training history
    try:
        nn.plot_training_history(history)
    except:
        print("Matplotlib not available for plotting")

if __name__ == "__main__":
    main()
