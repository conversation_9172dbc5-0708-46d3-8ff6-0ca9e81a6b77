"""
Main script to run all mathematical assignments
This script executes all the assignment solutions in sequence
"""

import sys
import traceback

def run_assignment(module_name, description):
    """Run a specific assignment module"""
    print(f"\n{'='*60}")
    print(f"RUNNING: {description}")
    print(f"{'='*60}")
    
    try:
        if module_name == "neural_network_modified":
            import neural_network_modified
            neural_network_modified.main()
        elif module_name == "linear_algebra_operations":
            import linear_algebra_operations
            linear_algebra_operations.main()
        elif module_name == "calculus_problems":
            import calculus_problems
            calculus_problems.main()
        elif module_name == "probability_statistics":
            import probability_statistics
            probability_statistics.main()
        
        print(f"\n✅ {description} completed successfully!")
        
    except ImportError as e:
        print(f"❌ Import error in {description}: {e}")
        print("Please install required packages using: pip install -r requirements.txt")
    except Exception as e:
        print(f"❌ Error in {description}: {e}")
        print("Full traceback:")
        traceback.print_exc()

def main():
    """Main function to run all assignments"""
    print("Mathematical Assignments - Complete Solution Suite")
    print("=" * 60)
    print("This script will run all assignment solutions:")
    print("1. Neural Network with Modified Input Features")
    print("2. Linear Algebra Operations")
    print("3. Calculus Problems (Optimization & Integration)")
    print("4. Probability and Statistics")
    print("=" * 60)
    
    # Check if user wants to run all or specific assignments
    print("\nOptions:")
    print("1. Run all assignments")
    print("2. Run specific assignment")
    print("3. Exit")
    
    try:
        choice = input("\nEnter your choice (1-3): ").strip()
    except KeyboardInterrupt:
        print("\n\nExiting...")
        return
    
    if choice == "1":
        # Run all assignments
        assignments = [
            ("neural_network_modified", "Neural Network with Modified Input Features"),
            ("linear_algebra_operations", "Linear Algebra Operations"),
            ("calculus_problems", "Calculus Problems"),
            ("probability_statistics", "Probability and Statistics")
        ]
        
        for module_name, description in assignments:
            run_assignment(module_name, description)
            
    elif choice == "2":
        # Run specific assignment
        print("\nSelect assignment to run:")
        print("1. Neural Network with Modified Input Features")
        print("2. Linear Algebra Operations")
        print("3. Calculus Problems")
        print("4. Probability and Statistics")
        
        try:
            assignment_choice = input("Enter assignment number (1-4): ").strip()
        except KeyboardInterrupt:
            print("\n\nExiting...")
            return
        
        assignments = {
            "1": ("neural_network_modified", "Neural Network with Modified Input Features"),
            "2": ("linear_algebra_operations", "Linear Algebra Operations"),
            "3": ("calculus_problems", "Calculus Problems"),
            "4": ("probability_statistics", "Probability and Statistics")
        }
        
        if assignment_choice in assignments:
            module_name, description = assignments[assignment_choice]
            run_assignment(module_name, description)
        else:
            print("Invalid choice!")
            
    elif choice == "3":
        print("Exiting...")
        return
    else:
        print("Invalid choice!")
        return
    
    print(f"\n{'='*60}")
    print("ALL ASSIGNMENTS COMPLETED!")
    print("=" * 60)
    print("\nSummary of what was accomplished:")
    print("✅ Neural Network: Implemented modified input features (x1, x2, x3, x1², x2²)")
    print("✅ Linear Algebra: Matrix operations, eigenvalues, eigenvectors")
    print("✅ Calculus: Optimization problems and numerical integration")
    print("✅ Statistics: Normal distribution analysis and statistical tests")
    print("\nAll solutions include both theoretical explanations and practical implementations.")

if __name__ == "__main__":
    main()
