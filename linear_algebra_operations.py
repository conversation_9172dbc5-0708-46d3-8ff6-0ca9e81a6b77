"""
Linear Algebra Operations Implementation
Complete implementation of matrix operations, eigenvalues, and eigenvectors
"""

import numpy as np
import scipy.linalg as la
from scipy.linalg import eig, inv, det
import matplotlib.pyplot as plt

class LinearAlgebraOperations:
    """Class for performing various linear algebra operations"""
    
    @staticmethod
    def matrix_multiplication(A, B):
        """Perform matrix multiplication"""
        try:
            result = np.dot(A, B)
            return result
        except ValueError as e:
            print(f"Matrix multiplication error: {e}")
            return None
    
    @staticmethod
    def matrix_determinant(A):
        """Calculate matrix determinant"""
        try:
            return det(A)
        except Exception as e:
            print(f"Determinant calculation error: {e}")
            return None
    
    @staticmethod
    def matrix_inverse(A):
        """Calculate matrix inverse"""
        try:
            det_A = det(A)
            if abs(det_A) < 1e-10:
                print("Matrix is singular (determinant ≈ 0), inverse does not exist")
                return None
            return inv(A)
        except Exception as e:
            print(f"Matrix inverse error: {e}")
            return None
    
    @staticmethod
    def eigenvalues_eigenvectors(A):
        """Calculate eigenvalues and eigenvectors"""
        try:
            eigenvals, eigenvecs = eig(A)
            return eigenvals, eigenvecs
        except Exception as e:
            print(f"Eigenvalue calculation error: {e}")
            return None, None
    
    @staticmethod
    def characteristic_polynomial(A):
        """Calculate characteristic polynomial coefficients"""
        eigenvals, _ = LinearAlgebraOperations.eigenvalues_eigenvectors(A)
        if eigenvals is not None:
            # For a 2x2 matrix, characteristic polynomial is λ² - trace(A)λ + det(A)
            if A.shape == (2, 2):
                trace_A = np.trace(A)
                det_A = det(A)
                return [1, -trace_A, det_A]  # coefficients of λ², λ¹, λ⁰
        return None

def solve_assignment_problems():
    """Solve the specific problems from the assignment"""
    print("Linear Algebra Assignment Solutions")
    print("=" * 40)
    
    # Problem 1: Matrix Operations
    print("\nProblem 1: Matrix Operations")
    print("-" * 30)
    
    # Define matrices A and B
    A = np.array([[2, 1, 3],
                  [0, 4, 1],
                  [1, 2, 0]])
    
    B = np.array([[1, 0],
                  [2, 1],
                  [0, 3]])
    
    print("Matrix A:")
    print(A)
    print("\nMatrix B:")
    print(B)
    
    # 1. Matrix Multiplication AB
    AB = LinearAlgebraOperations.matrix_multiplication(A, B)
    print(f"\n1. Matrix Multiplication AB:")
    print(AB)
    
    # Verify manual calculation
    expected_AB = np.array([[4, 10],
                           [8, 7],
                           [5, 2]])
    print(f"Expected result: \n{expected_AB}")
    print(f"Calculation correct: {np.allclose(AB, expected_AB)}")
    
    # 2. Determinant of A
    det_A = LinearAlgebraOperations.matrix_determinant(A)
    print(f"\n2. Determinant of A: {det_A}")
    print(f"Expected: -15, Calculation correct: {abs(det_A - (-15)) < 1e-10}")
    
    # 3. Inverse of A
    A_inv = LinearAlgebraOperations.matrix_inverse(A)
    print(f"\n3. Inverse of A:")
    if A_inv is not None:
        print(A_inv)
        # Verify A * A^(-1) = I
        identity_check = np.dot(A, A_inv)
        print(f"Verification A * A^(-1) ≈ I:")
        print(identity_check)
        print(f"Is identity matrix: {np.allclose(identity_check, np.eye(3))}")
    
    # Problem 2: Eigenvalues and Eigenvectors
    print("\n\nProblem 2: Eigenvalues and Eigenvectors")
    print("-" * 40)
    
    C = np.array([[3, 1],
                  [0, 2]])
    
    print("Matrix C:")
    print(C)
    
    # Calculate characteristic polynomial
    char_poly = LinearAlgebraOperations.characteristic_polynomial(C)
    print(f"\nCharacteristic polynomial coefficients: {char_poly}")
    print("Characteristic polynomial: λ² - 5λ + 6")
    
    # Calculate eigenvalues and eigenvectors
    eigenvals, eigenvecs = LinearAlgebraOperations.eigenvalues_eigenvectors(C)
    
    print(f"\nEigenvalues: {eigenvals}")
    print(f"Expected eigenvalues: [2, 3]")
    print(f"Calculation correct: {np.allclose(sorted(eigenvals), [2, 3])}")
    
    print(f"\nEigenvectors:")
    for i, (val, vec) in enumerate(zip(eigenvals, eigenvecs.T)):
        print(f"λ{i+1} = {val:.4f}, v{i+1} = {vec}")
        
        # Verify Cv = λv
        Cv = np.dot(C, vec)
        lambda_v = val * vec
        print(f"Verification Cv = λv: {np.allclose(Cv, lambda_v)}")

def demonstrate_advanced_operations():
    """Demonstrate additional linear algebra operations"""
    print("\n\nAdvanced Linear Algebra Operations")
    print("=" * 40)
    
    # Matrix decompositions
    A = np.random.randn(4, 4)
    A = A + A.T  # Make symmetric for better eigenvalue properties
    
    print("Random symmetric matrix A:")
    print(A)
    
    # SVD decomposition
    U, s, Vt = np.linalg.svd(A)
    print(f"\nSVD decomposition:")
    print(f"Singular values: {s}")
    
    # QR decomposition
    Q, R = np.linalg.qr(A)
    print(f"\nQR decomposition verification:")
    print(f"Q * R ≈ A: {np.allclose(np.dot(Q, R), A)}")
    print(f"Q is orthogonal: {np.allclose(np.dot(Q.T, Q), np.eye(4))}")
    
    # Condition number
    cond_num = np.linalg.cond(A)
    print(f"\nCondition number: {cond_num:.4f}")
    
    # Matrix norms
    print(f"Frobenius norm: {np.linalg.norm(A, 'fro'):.4f}")
    print(f"2-norm: {np.linalg.norm(A, 2):.4f}")

def visualize_eigenvalues():
    """Visualize eigenvalues and eigenvectors"""
    try:
        # Create a 2x2 matrix for visualization
        A = np.array([[3, 1],
                      [0, 2]])
        
        eigenvals, eigenvecs = eig(A)
        
        # Create a plot
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # Plot 1: Original vectors and transformed vectors
        original_vectors = np.array([[1, 0], [0, 1], [1, 1], [-1, 1]])
        transformed_vectors = np.array([A @ v for v in original_vectors])
        
        ax1.quiver(0, 0, original_vectors[:, 0], original_vectors[:, 1], 
                  angles='xy', scale_units='xy', scale=1, color='blue', alpha=0.7, label='Original')
        ax1.quiver(0, 0, transformed_vectors[:, 0], transformed_vectors[:, 1], 
                  angles='xy', scale_units='xy', scale=1, color='red', alpha=0.7, label='Transformed')
        
        # Plot eigenvectors
        for i, (val, vec) in enumerate(zip(eigenvals, eigenvecs.T)):
            ax1.quiver(0, 0, vec[0], vec[1], angles='xy', scale_units='xy', scale=1, 
                      color='green', width=0.005, label=f'Eigenvector {i+1}')
        
        ax1.set_xlim(-3, 4)
        ax1.set_ylim(-3, 4)
        ax1.grid(True)
        ax1.legend()
        ax1.set_title('Linear Transformation and Eigenvectors')
        ax1.set_aspect('equal')
        
        # Plot 2: Eigenvalue spectrum
        ax2.scatter(eigenvals.real, eigenvals.imag, s=100, c='red')
        ax2.set_xlabel('Real Part')
        ax2.set_ylabel('Imaginary Part')
        ax2.set_title('Eigenvalue Spectrum')
        ax2.grid(True)
        
        for i, val in enumerate(eigenvals):
            ax2.annotate(f'λ{i+1}={val:.2f}', (val.real, val.imag), 
                        xytext=(5, 5), textcoords='offset points')
        
        plt.tight_layout()
        plt.show()
        
    except ImportError:
        print("Matplotlib not available for visualization")

def main():
    """Main function to run all linear algebra operations"""
    solve_assignment_problems()
    demonstrate_advanced_operations()
    visualize_eigenvalues()

if __name__ == "__main__":
    main()
